/**
 * 后台用户管理模块
 */
class AdminUsersModule {
    constructor() {
        this.dependencies = ['createModal', 'closeModal', 'showMessage', 'checkPermission', 'showNoPermission'];
        this.init();
    }

    init() {
        this.ensureDependencies().then(() => {
            console.log('后台用户管理模块依赖加载完成');
        });
    }

    async ensureDependencies() {
        for (const dep of this.dependencies) {
            while (typeof window[dep] !== 'function') {
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        }
    }

    // 加载用户列表
    loadUsers() {
        if (!checkPermission('can_manage_users')) {
            showNoPermission('users', 7, '用户管理');
            return;
        }
        const tbody = document.querySelector('#users-table tbody');
        if (!tbody) return;
        tbody.innerHTML = '<tr><td colspan="7" class="empty-state">加载中...</td></tr>';

        Promise.all([
            fetch('/api/users').then(res => res.json()),
            fetch('/api/departments').then(res => res.json()),
            fetch('/api/user-groups').then(res => res.json())
        ]).then(([users, departments, userGroups]) => {
            if (!Array.isArray(users)) {
                throw new Error("收到的用户数据格式不正确");
            }
            if (!Array.isArray(departments)) {
                console.error("收到的部门数据格式不正确");
                departments = [];
            }
            if (!Array.isArray(userGroups)) {
                console.error("收到的用户组数据格式不正确");
                userGroups = [];
            }

            const departmentMap = new Map(departments.map(d => [d.id, d.name]));
            const groupMap = new Map(userGroups.map(g => [g.id, g.name]));

            if (users.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="empty-state">暂无用户数据</td></tr>';
                return;
            }
            
            tbody.innerHTML = users.map(user => {
                const actionButtons = [];
                if (checkPermission('can_manage_users_edit')) {
                    actionButtons.push(`<button class=\"btn-edit\" onclick=\"adminUsersModule.editUser(${user.id})\">编辑</button>`);
                }
                if (checkPermission('can_manage_users_delete')) {
                    actionButtons.push(`<button class=\"btn-delete\" onclick=\"adminUsersModule.deleteUser(${user.id})\">删除</button>`);
                }
                return `
                <tr>
                    <td>${user.id}</td>
                    <td>${user.card_id}</td>
                    <td>${user.name}</td>
                    <td>${user.code || ''}</td>
                    <td>${departmentMap.get(user.department_id) || ''}</td>
                    <td>${groupMap.get(user.group_id) || ''}</td>
                    <td>${actionButtons.join('')}</td>
                </tr>
                `;
            }).join('');
        }).catch(error => {
            console.error('Error loading users:', error);
            tbody.innerHTML = '<tr><td colspan="7" class="empty-state">加载失败</td></tr>';
        });
    }

    // 显示添加用户模态框
    showAddUserModal() {
        Promise.all([
            fetch('/api/departments').then(r => r.json()),
            fetch('/api/user-groups').then(r => r.json())
        ]).then(([departments, groups]) => {
            const modal = createModal('添加用户', `
                <div class="form-group">
                    <label for="user-card-id">员工卡ID</label>
                    <input type="text" id="user-card-id" required>
                    <div id="card-id-unique-msg" style="color: red; font-size: 13px; display: none;"></div>
                </div>
                <div class="form-group">
                    <label for="user-name">姓名</label>
                    <input type="text" id="user-name" required>
                </div>
                <div class="form-group">
                    <label for="user-code">代码</label>
                    <input type="text" id="user-code">
                    <div id="code-unique-msg" style="color: red; font-size: 13px; display: none;"></div>
                </div>
                <div class="form-group">
                    <label for="user-password">密码 (可选)</label>
                    <input type="password" id="user-password" placeholder="留空表示无密码登录">
                    <small style="color: #666; font-size: 12px;">留空表示用户可以无密码登录</small>
                </div>
                <div class="form-group">
                    <label for="user-department">部门</label>
                    <select id="user-department">
                        <option value="">选择部门</option>
                        ${departments.map(dept => `<option value="${dept.id}">${dept.name}</option>`).join('')}
                    </select>
                </div>
                <div class="form-group">
                    <label for="user-group">用户组</label>
                    <select id="user-group" required>
                        ${groups.map(group => `<option value="${group.id}">${group.name}</option>`).join('')}
                    </select>
                </div>
            `);
            const btnSave = modal.querySelector('.btn-save');
            const cardIdInput = document.getElementById('user-card-id');
            const codeInput = document.getElementById('user-code');
            const cardIdMsg = document.getElementById('card-id-unique-msg');
            const codeMsg = document.getElementById('code-unique-msg');

            let cardIdValid = true;
            let codeValid = true;

            async function checkUnique() {
                const cardId = cardIdInput.value.trim();
                const code = codeInput.value.trim();
                let url = '/api/users/check-unique?';
                if (cardId) url += `card_id=${encodeURIComponent(cardId)}`;
                if (code) url += (cardId ? '&' : '') + `code=${encodeURIComponent(code)}`;
                if (!cardId && !code) {
                    cardIdMsg.style.display = 'none';
                    codeMsg.style.display = 'none';
                    cardIdValid = true;
                    codeValid = true;
                    btnSave.disabled = false;
                    return;
                }
                try {
                    const res = await fetch(url);
                    const data = await res.json();
                    cardIdValid = !data.card_id_exists;
                    codeValid = !data.code_exists;
                    if (!cardIdValid) {
                        cardIdMsg.textContent = '该员工卡ID已被占用';
                        cardIdMsg.style.display = '';
                    } else {
                        cardIdMsg.style.display = 'none';
                    }
                    if (!codeValid && code) {
                        codeMsg.textContent = '该代码已被占用';
                        codeMsg.style.display = '';
                    } else {
                        codeMsg.style.display = 'none';
                    }
                    btnSave.disabled = !(cardIdValid && codeValid);
                } catch (e) {
                    cardIdMsg.textContent = '校验失败';
                    cardIdMsg.style.display = '';
                    btnSave.disabled = true;
                }
            }
            cardIdInput.addEventListener('input', checkUnique);
            codeInput.addEventListener('input', checkUnique);

            modal.querySelector('.btn-save').onclick = async () => {
                const cardId = cardIdInput.value.trim();
                const name = document.getElementById('user-name').value;
                const code = codeInput.value.trim();
                const password = document.getElementById('user-password').value.trim();
                const departmentId = document.getElementById('user-department').value || null;
                const groupId = document.getElementById('user-group').value;
                if (!cardId || !name || !groupId) {
                    alert('请填写必填字段');
                    return;
                }
                // 保存前再校验一次
                await checkUnique();
                if (!cardIdValid || !codeValid) {
                    showMessage('员工卡ID或代码已被占用，请修改后再保存', 'error');
                    return;
                }
                this.createUser({ card_id: cardId, name, code, password, department_id: departmentId, group_id: groupId });
                closeModal(modal);
            };
        }).catch(error => {
            console.error('Error loading data for user modal:', error);
            showMessage('加载数据失败', 'error');
        });
    }

    // 创建用户
    createUser(userData) {
        fetch('/api/users', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(userData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('用户创建成功', 'success');
                this.loadUsers();
            } else {
                showMessage(data.error || '创建失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error creating user:', error);
            showMessage('创建失败', 'error');
        });
    }

    // 编辑用户
    editUser(id) {
        fetch('/api/users')
            .then(response => response.json())
            .then(users => {
                const user = users.find(u => u.id === id);
                if (!user) {
                    showMessage('用户不存在', 'error');
                    return;
                }
                Promise.all([
                    fetch('/api/departments').then(r => r.json()),
                    fetch('/api/user-groups').then(r => r.json())
                ]).then(([departments, groups]) => {
                    const modal = createModal('编辑用户', `
                        <div class="form-group">
                            <label for="user-card-id">员工卡ID</label>
                            <input type="text" id="user-card-id" value="${user.card_id}" required>
                            <div id="card-id-unique-msg" style="color: red; font-size: 13px; display: none;"></div>
                        </div>
                        <div class="form-group">
                            <label for="user-name">姓名</label>
                            <input type="text" id="user-name" value="${user.name}" required>
                        </div>
                        <div class="form-group">
                            <label for="user-code">代码</label>
                            <input type="text" id="user-code" value="${user.code || ''}">
                            <div id="code-unique-msg" style="color: red; font-size: 13px; display: none;"></div>
                        </div>
                        <div class="form-group">
                            <label for="user-password">密码 (可选)</label>
                            <input type="password" id="user-password" placeholder="留空表示不修改密码">
                            <small style="color: #666; font-size: 12px;">留空表示不修改当前密码设置</small>
                        </div>
                        <div class="form-group">
                            <label for="user-department">部门</label>
                            <select id="user-department">
                                <option value="">选择部门</option>
                                ${departments.map(dept => `<option value="${dept.id}" ${dept.id === user.department_id ? 'selected' : ''}>${dept.name}</option>`).join('')}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="user-group">用户组</label>
                            <select id="user-group" required>
                                ${groups.map(group => `<option value="${group.id}" ${group.id === user.group_id ? 'selected' : ''}>${group.name}</option>`).join('')}
                            </select>
                        </div>
                    `);
                    const btnSave = modal.querySelector('.btn-save');
                    const cardIdInput = document.getElementById('user-card-id');
                    const codeInput = document.getElementById('user-code');
                    const cardIdMsg = document.getElementById('card-id-unique-msg');
                    const codeMsg = document.getElementById('code-unique-msg');

                    let cardIdValid = true;
                    let codeValid = true;

                    async function checkUnique() {
                        const cardId = cardIdInput.value.trim();
                        const code = codeInput.value.trim();
                        // 只有输入内容与原值不同才校验
                        let needCheckCardId = cardId && cardId !== user.card_id;
                        let needCheckCode = code && code !== (user.code || '');
                        if (!needCheckCardId && !needCheckCode) {
                            cardIdMsg.style.display = 'none';
                            codeMsg.style.display = 'none';
                            cardIdValid = true;
                            codeValid = true;
                            btnSave.disabled = false;
                            return;
                        }
                        let url = '/api/users/check-unique?';
                        if (needCheckCardId) url += `card_id=${encodeURIComponent(cardId)}`;
                        if (needCheckCode) url += (needCheckCardId ? '&' : '') + `code=${encodeURIComponent(code)}`;
                        try {
                            const res = await fetch(url);
                            const data = await res.json();
                            cardIdValid = needCheckCardId ? !data.card_id_exists : true;
                            codeValid = needCheckCode ? !data.code_exists : true;
                            if (!cardIdValid) {
                                cardIdMsg.textContent = '该员工卡ID已被占用';
                                cardIdMsg.style.display = '';
                            } else {
                                cardIdMsg.style.display = 'none';
                            }
                            if (!codeValid && needCheckCode) {
                                codeMsg.textContent = '该代码已被占用';
                                codeMsg.style.display = '';
                            } else {
                                codeMsg.style.display = 'none';
                            }
                            btnSave.disabled = !(cardIdValid && codeValid);
                        } catch (e) {
                            cardIdMsg.textContent = '校验失败';
                            cardIdMsg.style.display = '';
                            btnSave.disabled = true;
                        }
                    }
                    cardIdInput.addEventListener('input', checkUnique);
                    codeInput.addEventListener('input', checkUnique);

                    modal.querySelector('.btn-save').onclick = async () => {
                        const cardId = cardIdInput.value.trim();
                        const name = document.getElementById('user-name').value;
                        const code = codeInput.value.trim();
                        const password = document.getElementById('user-password').value.trim();
                        const departmentId = document.getElementById('user-department').value || null;
                        const groupId = document.getElementById('user-group').value;
                        if (!cardId || !name || !groupId) {
                            alert('请填写必填字段');
                            return;
                        }
                        // 保存前再校验一次
                        await checkUnique();
                        if (!cardIdValid || !codeValid) {
                            showMessage('员工卡ID或代码已被占用，请修改后再保存', 'error');
                            return;
                        }

                        const updateData = { card_id: cardId, name, code, department_id: departmentId, group_id: groupId };
                        // 只有输入了密码才包含在更新数据中
                        if (password !== '') {
                            updateData.password = password;
                        }

                        this.updateUser(id, updateData);
                        closeModal(modal);
                    };
                });
            })
            .catch(error => {
                console.error('Error loading user:', error);
                showMessage('加载用户失败', 'error');
            });
    }

    // 更新用户
    updateUser(id, userData) {
        fetch(`/api/users/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(userData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('用户更新成功', 'success');
                this.loadUsers();
            } else {
                showMessage(data.error || '更新失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error updating user:', error);
            showMessage('更新失败', 'error');
        });
    }

    // 删除用户
    deleteUser(id) {
        if (!confirm('确定要删除这个用户吗？')) return;
        fetch(`/api/users/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('用户删除成功', 'success');
                this.loadUsers();
            } else {
                showMessage(data.error || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting user:', error);
            showMessage('删除失败', 'error');
        });
    }
}

window.adminUsersModule = new AdminUsersModule(); 