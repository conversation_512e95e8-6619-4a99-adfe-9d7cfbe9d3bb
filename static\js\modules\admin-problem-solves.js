// 问题解决模块
const adminProblemSolvesModule = (() => {
    function loadProblemSolves() {
        const tbody = document.querySelector('#problem-solve-table tbody');
        if (!tbody) return;
        
        tbody.innerHTML = '<tr><td colspan="7" class="empty-state">加载中...</td></tr>';
        
        fetch('/api/problem-solves')
            .then(response => response.json())
            .then(problems => {
                if (problems.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="7" class="empty-state">暂无问题记录</td></tr>';
                    return;
                }
                
                tbody.innerHTML = problems.map(problem => {
                    // 根据权限生成操作按钮
                    const actionButtons = [];
                    const canReply = window.checkPermission && window.checkPermission('can_reply_problem_solve');
                    const canDelete = window.checkPermission && window.checkPermission('can_delete_problem_solve');
                    if (canReply) {
                        actionButtons.push(`<button class="btn-edit" onclick="window.adminProblemSolvesModule.replyToProblemSolve(${problem.id})">回复</button>`);
                    }
                    if (canDelete) {
                        actionButtons.push(`<button class="btn-delete" onclick="window.adminProblemSolvesModule.deleteProblemSolve(${problem.id})">删除</button>`);
                    }
                    return `
                    <tr>
                        <td>${problem.id}</td>
                        <td>${problem.title || '无标题'}</td>
                        <td>${problem.submitted_by_name || '未知用户'}</td>
                        <td>${problem.content ? problem.content.substring(0, 50) + (problem.content.length > 50 ? '...' : '') : ''}</td>
                        <td>
                            <span class="status-${problem.reply ? 'replied' : 'pending'}">
                                ${problem.reply ? '已回复' : '待处理'}
                            </span>
                        </td>
                        <td>${window.formatDate ? window.formatDate(problem.created_at) : problem.created_at}</td>
                        <td>
                                ${actionButtons.join('')}
                        </td>
                    </tr>
                    `;
                }).join('');
            })
            .catch(error => {
                console.error('Error loading problem solves:', error);
                tbody.innerHTML = '<tr><td colspan="7" class="empty-state">加载失败</td></tr>';
            });
    }

    function replyToProblemSolve(id) {
        fetch('/api/problem-solves')
            .then(response => response.json())
            .then(problems => {
                const problem = problems.find(p => p.id === id);
                if (!problem) {
                    window.showMessage && window.showMessage('问题不存在', 'error');
                    return;
                }
                // 创建modal弹窗
                const modal = document.createElement('div');
                modal.className = 'modal';
                modal.innerHTML = `
                    <div class="modal-content" style="max-width: 500px;">
                        <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <h3 style="margin: 0; color: #333;">回复问题 #${id}</h3>
                            <button class="close" onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">&times;</button>
                        </div>
                        <div class="form-group" style="margin-bottom: 15px;">
                            <strong>原问题：</strong>
                            <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 5px;">${problem.content || ''}</div>
                        </div>
                        <div class="form-group">
                            <label style="display: block; margin-bottom: 8px; font-weight: 600;">回复内容：</label>
                            <textarea id="replyContent" placeholder="请输入您的回复..." required style="width: 100%; min-height: 80px; border: 1px solid #ddd; border-radius: 4px; padding: 8px;">${problem.reply || ''}</textarea>
                        </div>
                        <div class="modal-actions" style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                            <button type="button" class="btn-cancel" onclick="this.closest('.modal').remove()">取消</button>
                            <button type="button" id="submitReplyBtn" style="padding: 10px 20px; background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: 600;">提交回复</button>
                        </div>
                    </div>
                `;
                document.body.appendChild(modal);
                // 关闭modal时点击遮罩
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        modal.remove();
                    }
                });
                // 提交回复
                modal.querySelector('#submitReplyBtn').onclick = function() {
                    const reply = modal.querySelector('#replyContent').value.trim();
                    if (!reply) {
                        alert('请填写回复内容');
                        return;
                    }
                    fetch(`/api/problem-solves/${id}/reply`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ reply: reply })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.showMessage && window.showMessage('回复成功', 'success');
                            modal.remove();
                            loadProblemSolves();
                        } else {
                            window.showMessage && window.showMessage(data.error || '回复失败', 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error replying to problem solve:', error);
                        window.showMessage && window.showMessage('回复失败', 'error');
                    });
                };
            })
            .catch(error => {
                console.error('Error loading problem solve:', error);
                window.showMessage && window.showMessage('加载问题失败', 'error');
            });
    }

    function deleteProblemSolve(id) {
        if (!confirm('确定要删除这个问题记录吗？')) return;
        fetch(`/api/problem-solves/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.showMessage && window.showMessage('删除成功', 'success');
                loadProblemSolves();
            } else {
                window.showMessage && window.showMessage(data.error || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting problem solve:', error);
            window.showMessage && window.showMessage('删除失败', 'error');
        });
    }

    return {
        loadProblemSolves,
        replyToProblemSolve,
        deleteProblemSolve
    };
})();

window.adminProblemSolvesModule = adminProblemSolvesModule; 