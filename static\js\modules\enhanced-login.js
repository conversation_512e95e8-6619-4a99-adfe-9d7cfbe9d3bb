// 增强登录模块
class EnhancedLoginModule {
    constructor() {
        this.isInitialized = false;
        this.currentCardId = '';
        this.needsPassword = false;
        this.failedAttempts = 0;
        this.maxFailedAttempts = 3;
    }

    // 初始化模块
    init() {
        if (this.isInitialized) return;
        
        this.setupLoginForm();
        this.isInitialized = true;
    }

    // 设置登录表单
    setupLoginForm() {
        const form = document.querySelector('.login-container form');
        if (!form) return;

        const cardInput = form.querySelector('input[name="card_id"]');
        const submitBtn = form.querySelector('button[type="submit"]');
        
        if (!cardInput || !submitBtn) return;

        // Create password input (initially hidden)
        const passwordInput = document.createElement('input');
        passwordInput.type = 'password';
        passwordInput.name = 'password';
        passwordInput.placeholder = this.t('enter_password') || 'Enter Password';
        passwordInput.style.display = 'none';
        passwordInput.style.marginTop = '10px';

        // Insert password input after card input
        cardInput.parentNode.insertBefore(passwordInput, submitBtn);

        // Create user info display
        const userInfo = document.createElement('div');
        userInfo.className = 'user-info';
        userInfo.style.cssText = `
            margin: 10px 0;
            padding: 10px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 6px;
            display: none;
            font-weight: 600;
            color: #667eea;
        `;
        cardInput.parentNode.insertBefore(userInfo, passwordInput);

        // Modify form behavior
        form.addEventListener('submit', (e) => {
            if (!this.needsPassword) {
                e.preventDefault();
                this.checkUser(cardInput.value.trim());
            }
            // If needsPassword is true, let form submit normally
        });

        // Handle card input changes
        cardInput.addEventListener('input', () => {
            this.resetForm();
        });
    }

    // 检查用户
    checkUser(cardId) {
        if (!cardId) {
            this.showMessage(this.t('enter_card_id') || 'Please enter your card ID', 'error');
            return;
        }

        fetch('/api/check-user', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ card_id: cardId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.exists) {
                this.currentCardId = cardId;
                this.showUserInfo(data.name, data.needs_password);
                
                if (data.needs_password) {
                    this.showPasswordInput();
                } else {
                    this.submitLogin();
                }
            } else {
                this.showMessage(this.t('user_not_found') || 'User not found', 'error');
            }
        })
        .catch(error => {
            console.error('Error checking user:', error);
            this.showMessage(this.t('check_failed') || 'Failed to check user', 'error');
        });
    }

    // 显示用户信息
    showUserInfo(name, needsPassword) {
        const userInfo = document.querySelector('.user-info');
        if (userInfo) {
            userInfo.innerHTML = `
                <strong>${this.t('welcome') || 'Welcome'}, ${name}!</strong>
                ${needsPassword ? 
                    `<br><small>${this.t('password_required') || 'Password required to continue'}</small>` : 
                    `<br><small>${this.t('logging_in') || 'Logging you in...'}</small>`
                }
            `;
            userInfo.style.display = 'block';
        }
    }

    // 显示密码输入框
    showPasswordInput() {
        const passwordInput = document.querySelector('input[name="password"]');
        const submitBtn = document.querySelector('button[type="submit"]');
        
        if (passwordInput && submitBtn) {
            passwordInput.style.display = 'block';
            passwordInput.focus();
            submitBtn.textContent = this.t('login') || 'Login';
            this.needsPassword = true;
        }
    }

    // 提交登录
    submitLogin() {
        const cardIdInput = document.querySelector('input[name="card_id"]');
        const passwordInput = document.querySelector('input[name="password"]');

        const cardId = cardIdInput ? cardIdInput.value : '';
        const password = passwordInput ? passwordInput.value : '';

        // 使用AJAX提交登录
        fetch('/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                card_id: cardId,
                password: password
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 登录成功，重定向到主页
                window.location.href = data.redirect;
            } else if (data.error) {
                // 登录失败
                if (data.error.includes('密码错误') || data.error.includes('Invalid password')) {
                    this.failedAttempts++;
                    this.showMessage(data.error, 'error');

                    if (this.failedAttempts >= this.maxFailedAttempts || data.show_reset) {
                        this.showPasswordResetOption();
                    }
                } else {
                    // 其他错误
                    this.showMessage(data.error, 'error');
                }
            }
        })
        .catch(error => {
            console.error('Login error:', error);
            this.showMessage(this.t('login_failed') || 'Login failed', 'error');
        });
    }

    // 重置表单
    resetForm() {
        const passwordInput = document.querySelector('input[name="password"]');
        const userInfo = document.querySelector('.user-info');
        const submitBtn = document.querySelector('button[type="submit"]');
        
        if (passwordInput) {
            passwordInput.style.display = 'none';
            passwordInput.value = '';
        }
        
        if (userInfo) {
            userInfo.style.display = 'none';
        }
        
        if (submitBtn) {
            submitBtn.textContent = this.t('continue') || 'Continue';
        }
        
        this.needsPassword = false;
        this.currentCardId = '';
        this.failedAttempts = 0;
    }

    // 显示密码重置选项
    showPasswordResetOption() {
        const form = document.querySelector('.login-container form');
        if (!form) return;

        // 检查是否已经显示了重置选项
        if (document.querySelector('.password-reset-options')) return;

        const resetOptions = document.createElement('div');
        resetOptions.className = 'password-reset-options';
        resetOptions.style.cssText = `
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #dc3545;
        `;

        resetOptions.innerHTML = `
            <p style="margin: 0 0 10px 0; color: #dc3545; font-weight: bold;">
                ${this.t('too_many_failed_attempts') || '失败次数过多。请重置密码或联系管理员。'}
            </p>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button type="button" class="btn-reset-password" style="
                    background: #007bff;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                ">
                    ${this.t('reset_password') || '重置密码'}
                </button>
                <button type="button" class="btn-contact-admin" style="
                    background: #6c757d;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                ">
                    ${this.t('contact_admin') || '联系管理员'}
                </button>
            </div>
        `;

        form.appendChild(resetOptions);

        // 添加重置密码按钮事件
        const resetBtn = resetOptions.querySelector('.btn-reset-password');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.showPasswordResetModal();
            });
        }

        // 添加联系管理员按钮事件
        const contactBtn = resetOptions.querySelector('.btn-contact-admin');
        if (contactBtn) {
            contactBtn.addEventListener('click', () => {
                this.showContactAdminInfo();
            });
        }
    }

    // 显示密码重置模态框
    showPasswordResetModal() {
        // 创建模态框
        const modal = document.createElement('div');
        modal.className = 'password-reset-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        `;

        modal.innerHTML = `
            <div style="
                background: white;
                padding: 30px;
                border-radius: 8px;
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
            ">
                <h3 style="margin: 0 0 20px 0; color: #333;">
                    ${this.t('reset_password') || '重置密码'}
                </h3>
                <p style="margin: 0 0 20px 0; color: #666;">
                    ${this.t('reset_password_instruction') || '请填写以下信息，所有信息必须与用户数据100%吻合才能重置密码。'}
                </p>
                <form class="password-reset-form">
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                            ${this.t('badge') || 'Badge'}:
                        </label>
                        <input type="text" name="badge" required style="
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-sizing: border-box;
                        ">
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                            ${this.t('full_name') || 'Full Name'}:
                        </label>
                        <input type="text" name="full_name" required style="
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-sizing: border-box;
                        ">
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                            ${this.t('login_code') || 'Login Code'}:
                        </label>
                        <input type="text" name="login_code" required style="
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-sizing: border-box;
                        ">
                    </div>
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                            ${this.t('department') || 'Department'}:
                        </label>
                        <input type="text" name="department" required style="
                            width: 100%;
                            padding: 8px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            box-sizing: border-box;
                        ">
                    </div>
                    <div style="display: flex; gap: 10px; justify-content: flex-end;">
                        <button type="button" class="btn-cancel" style="
                            background: #6c757d;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 4px;
                            cursor: pointer;
                        ">
                            ${this.t('cancel') || '取消'}
                        </button>
                        <button type="submit" style="
                            background: #dc3545;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 4px;
                            cursor: pointer;
                        ">
                            ${this.t('reset_password') || '重置密码'}
                        </button>
                    </div>
                </form>
            </div>
        `;

        document.body.appendChild(modal);

        // 添加事件监听器
        const form = modal.querySelector('.password-reset-form');
        const cancelBtn = modal.querySelector('.btn-cancel');

        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handlePasswordReset(form, modal);
        });

        cancelBtn.addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        // 点击模态框外部关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    // 处理密码重置
    handlePasswordReset(form, modal) {
        const formData = new FormData(form);
        const data = {
            badge: formData.get('badge'),
            full_name: formData.get('full_name'),
            login_code: formData.get('login_code'),
            department: formData.get('department')
        };

        fetch('/api/reset-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                document.body.removeChild(modal);
                this.showMessage(result.message || this.t('password_reset_success') || '密码已经清除，请直接登入！', 'success');
                this.failedAttempts = 0;
                this.hidePasswordResetOption();
            } else {
                this.showResetMessage(result.error || this.t('reset_failed') || '重置失败，请检查信息是否正确', 'error', form);
            }
        })
        .catch(error => {
            console.error('Password reset error:', error);
            this.showResetMessage(this.t('reset_failed') || '重置失败', 'error', form);
        });
    }

    // 显示重置消息
    showResetMessage(message, type, form) {
        let messageEl = form.querySelector('.reset-message');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.className = 'reset-message';
            messageEl.style.cssText = `
                margin: 10px 0;
                padding: 10px;
                border-radius: 4px;
                text-align: center;
            `;
            form.appendChild(messageEl);
        }

        messageEl.textContent = message;
        messageEl.style.background = type === 'error' ? '#f8d7da' : '#d4edda';
        messageEl.style.color = type === 'error' ? '#721c24' : '#155724';
        messageEl.style.border = type === 'error' ? '1px solid #f5c6cb' : '1px solid #c3e6cb';
    }

    // 隐藏密码重置选项
    hidePasswordResetOption() {
        const resetOptions = document.querySelector('.password-reset-options');
        if (resetOptions) {
            resetOptions.remove();
        }
    }

    // 显示联系管理员信息
    showContactAdminInfo() {
        alert(this.t('contact_admin_info') || '请联系系统管理员重置您的密码。');
    }

    // Translation helper
    t(key) {
        return window.i18n ? window.i18n.t(key) : null;
    }

    // Show message
    showMessage(message, type = 'info') {
        // Create or update message element
        let messageEl = document.querySelector('.login-message');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.className = 'login-message';
            messageEl.style.cssText = `
                margin: 10px 0;
                padding: 10px;
                border-radius: 4px;
                text-align: center;
            `;
            
            const form = document.querySelector('.login-container form');
            if (form) {
                form.appendChild(messageEl);
            }
        }
        
        messageEl.textContent = message;
        messageEl.className = `login-message ${type}`;
        
        // Style based on type
        if (type === 'error') {
            messageEl.style.background = '#f8d7da';
            messageEl.style.color = '#721c24';
            messageEl.style.border = '1px solid #f5c6cb';
        } else {
            messageEl.style.background = '#d4edda';
            messageEl.style.color = '#155724';
            messageEl.style.border = '1px solid #c3e6cb';
        }
        
        // Auto hide after 5 seconds
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.remove();
            }
        }, 5000);
    }

    // Destroy module
    destroy() {
        this.isInitialized = false;
    }
}

// Export module
window.EnhancedLoginModule = EnhancedLoginModule;
