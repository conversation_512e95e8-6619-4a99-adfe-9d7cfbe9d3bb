// 增强登录模块
class EnhancedLoginModule {
    constructor() {
        this.isInitialized = false;
        this.currentCardId = '';
        this.needsPassword = false;
    }

    // 初始化模块
    init() {
        if (this.isInitialized) return;
        
        this.setupLoginForm();
        this.isInitialized = true;
    }

    // 设置登录表单
    setupLoginForm() {
        const form = document.querySelector('.login-container form');
        if (!form) return;

        const cardInput = form.querySelector('input[name="card_id"]');
        const submitBtn = form.querySelector('button[type="submit"]');
        
        if (!cardInput || !submitBtn) return;

        // Create password input (initially hidden)
        const passwordInput = document.createElement('input');
        passwordInput.type = 'password';
        passwordInput.name = 'password';
        passwordInput.placeholder = this.t('enter_password') || 'Enter Password';
        passwordInput.style.display = 'none';
        passwordInput.style.marginTop = '10px';

        // Insert password input after card input
        cardInput.parentNode.insertBefore(passwordInput, submitBtn);

        // Create user info display
        const userInfo = document.createElement('div');
        userInfo.className = 'user-info';
        userInfo.style.cssText = `
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            display: none;
        `;
        cardInput.parentNode.insertBefore(userInfo, passwordInput);

        // Modify form behavior
        form.addEventListener('submit', (e) => {
            if (!this.needsPassword) {
                e.preventDefault();
                this.checkUser(cardInput.value.trim());
            }
            // If needsPassword is true, let form submit normally
        });

        // Handle card input changes
        cardInput.addEventListener('input', () => {
            this.resetForm();
        });
    }

    // 检查用户
    checkUser(cardId) {
        if (!cardId) {
            this.showMessage(this.t('enter_card_id') || 'Please enter your card ID', 'error');
            return;
        }

        fetch('/api/check-user', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ card_id: cardId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.exists) {
                this.currentCardId = cardId;
                this.showUserInfo(data.name, data.needs_password);
                
                if (data.needs_password) {
                    this.showPasswordInput();
                } else {
                    this.submitLogin();
                }
            } else {
                this.showMessage(this.t('user_not_found') || 'User not found', 'error');
            }
        })
        .catch(error => {
            console.error('Error checking user:', error);
            this.showMessage(this.t('check_failed') || 'Failed to check user', 'error');
        });
    }

    // 显示用户信息
    showUserInfo(name, needsPassword) {
        const userInfo = document.querySelector('.user-info');
        if (userInfo) {
            userInfo.innerHTML = `
                <strong>${this.t('welcome') || 'Welcome'}, ${name}!</strong>
                ${needsPassword ? 
                    `<br><small>${this.t('password_required') || 'Password required to continue'}</small>` : 
                    `<br><small>${this.t('logging_in') || 'Logging you in...'}</small>`
                }
            `;
            userInfo.style.display = 'block';
        }
    }

    // 显示密码输入框
    showPasswordInput() {
        const passwordInput = document.querySelector('input[name="password"]');
        const submitBtn = document.querySelector('button[type="submit"]');
        
        if (passwordInput && submitBtn) {
            passwordInput.style.display = 'block';
            passwordInput.focus();
            submitBtn.textContent = this.t('login') || 'Login';
            this.needsPassword = true;
        }
    }

    // 提交登录
    submitLogin() {
        const form = document.querySelector('.login-container form');
        if (form) {
            form.submit();
        }
    }

    // 重置表单
    resetForm() {
        const passwordInput = document.querySelector('input[name="password"]');
        const userInfo = document.querySelector('.user-info');
        const submitBtn = document.querySelector('button[type="submit"]');
        
        if (passwordInput) {
            passwordInput.style.display = 'none';
            passwordInput.value = '';
        }
        
        if (userInfo) {
            userInfo.style.display = 'none';
        }
        
        if (submitBtn) {
            submitBtn.textContent = this.t('continue') || 'Continue';
        }
        
        this.needsPassword = false;
        this.currentCardId = '';
    }

    // Translation helper
    t(key) {
        return window.i18n ? window.i18n.t(key) : null;
    }

    // Show message
    showMessage(message, type = 'info') {
        // Create or update message element
        let messageEl = document.querySelector('.login-message');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.className = 'login-message';
            messageEl.style.cssText = `
                margin: 10px 0;
                padding: 10px;
                border-radius: 4px;
                text-align: center;
            `;
            
            const form = document.querySelector('.login-container form');
            if (form) {
                form.appendChild(messageEl);
            }
        }
        
        messageEl.textContent = message;
        messageEl.className = `login-message ${type}`;
        
        // Style based on type
        if (type === 'error') {
            messageEl.style.background = '#f8d7da';
            messageEl.style.color = '#721c24';
            messageEl.style.border = '1px solid #f5c6cb';
        } else {
            messageEl.style.background = '#d4edda';
            messageEl.style.color = '#155724';
            messageEl.style.border = '1px solid #c3e6cb';
        }
        
        // Auto hide after 5 seconds
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.remove();
            }
        }, 5000);
    }

    // Destroy module
    destroy() {
        this.isInitialized = false;
    }
}

// Export module
window.EnhancedLoginModule = EnhancedLoginModule;
