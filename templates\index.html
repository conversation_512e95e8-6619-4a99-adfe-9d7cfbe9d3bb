<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="page_title">标签打印系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css', v=version) }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/chat.css', v=version) }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/i18n.css', v=version) }}">
</head>
<body>
    <header>
        <div class="header-left">
            <span class="title" data-i18n="label_printing">标签打印</span>
            <select id="department-select">
                <!-- Departments will be loaded here -->
            </select>
        </div>
        <div class="marquee">
            <p>这里是跑马灯公告...</p>
        </div>
        <div class="header-right">
            {% if permissions.can_manage_backend %}
            <a href="{{ url_for('admin') }}" class="btn-admin" data-i18n="admin_panel">后台管理</a>
            {% endif %}
            {% if permissions.can_view_problem_solve %}
            <a href="{{ url_for('problem_solve_list') }}" class="btn-problem-solve" data-i18n="problem_solve_title">
                Problem Solve
                <span class="bell-badge" id="psBadge" style="display:none"></span>
            </a>
            {% endif %}
            <a href="#" class="btn-feedback" data-i18n="feedback">反馈</a>
            {% if user_info %}
            <div class="user-dropdown">
                <span class="user-info">
                    {{ user_info.name }} ({{ user_info.department }})
                    <span class="dropdown-arrow">▼</span>
                </span>
                <div class="user-dropdown-menu">
                    <div class="language-menu-item">
                        <span data-i18n="language">Language</span>
                        <span class="submenu-arrow">▶</span>
                        <div class="language-submenu">
                            <div class="language-option" data-lang="en">
                                <span class="flag">🇺🇸</span>
                                <span>English</span>
                            </div>
                            <div class="language-option" data-lang="es">
                                <span class="flag">🇪🇸</span>
                                <span>Español</span>
                            </div>
                            <div class="language-option" data-lang="zh">
                                <span class="flag">🇨🇳</span>
                                <span>中文</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}
            <span class="notification-bell" id="notificationBell">🔔<span class="bell-badge" id="notificationBadge" style="display:none"></span></span>
            <a href="{{ url_for('logout') }}" data-i18n="logout">登出</a>
        </div>
    </header>

    <main>
        <!-- Search Section -->
        <div class="search-section">
            <div class="search-container">
                <input type="text" id="searchInput" class="search-input" data-i18n-placeholder="search_placeholder" placeholder="输入标签名称或关键词搜索..." autocomplete="off">
                <div id="searchResults" class="search-results"></div>
            </div>
        </div>

        <div class="label-grid">
            <!-- Label buttons will be generated here by JavaScript -->
        </div>
    </main>

    {% if permissions.can_use_chat %}
    <div class="chat-float">
        <span>💬</span>
    </div>
    {% endif %}

    <script type="text/javascript">
    window.userPermissions = {{ permissions|tojson | safe }};
    window.currentUserId = {{ user_id|tojson }};
    window.currentUserName = {{ user_name|tojson }};
    window.userInfo = {{ user_info|tojson }};
    </script>
    <script src="{{ url_for('static', filename='js/i18n.js', v=version) }}"></script>
    <script src="{{ url_for('static', filename='js/main.js', v=version) }}"></script>
    <script src="{{ url_for('static', filename='js/modules/feedback.js', v=version) }}"></script>
    <script src="{{ url_for('static', filename='js/modules/announcements.js', v=version) }}"></script>
    <script src="{{ url_for('static', filename='js/modules/chat.js', v=version) }}"></script>
    <script src="{{ url_for('static', filename='js/modules/knowledge-share.js', v=version) }}"></script>
    <script src="{{ url_for('static', filename='js/modules/utils.js') }}"></script>
    <script src="{{ url_for('static', filename='js/modules/search.js') }}"></script>
    <script src="{{ url_for('static', filename='js/modules/notifications.js') }}"></script>
</body>
</html>
