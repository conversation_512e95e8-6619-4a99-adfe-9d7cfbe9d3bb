// 用户注册模块
class RegistrationModule {
    constructor() {
        this.isInitialized = false;
        this.departments = []; // Will be loaded from API
    }

    // 初始化注册模块
    init() {
        if (this.isInitialized) return;

        this.checkRegistrationEnabled();

        this.isInitialized = true;
    }

    // 检查是否启用注册
    checkRegistrationEnabled() {
        fetch('/api/registration-enabled')
            .then(response => response.json())
            .then(data => {
                if (data.enabled) {
                    this.setupSignUpButton();
                    this.loadDepartments();
                }
            })
            .catch(error => {
                console.error('Error checking registration status:', error);
            });
    }

    // 设置注册按钮
    setupSignUpButton() {
        const loginForm = document.querySelector('.login-container form');
        if (!loginForm) return;

        // Create sign up button
        const signUpBtn = document.createElement('button');
        signUpBtn.type = 'button';
        signUpBtn.className = 'signup-btn';
        signUpBtn.textContent = this.t('sign_up') || 'Sign Up';
        signUpBtn.style.cssText = `
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-left: 10px;
            transition: background-color 0.2s;
        `;

        signUpBtn.addEventListener('mouseenter', () => {
            signUpBtn.style.backgroundColor = '#5a6268';
        });

        signUpBtn.addEventListener('mouseleave', () => {
            signUpBtn.style.backgroundColor = '#6c757d';
        });

        signUpBtn.addEventListener('click', () => {
            this.showRegistrationModal();
        });

        // Insert after login button
        const loginBtn = loginForm.querySelector('button[type="submit"]');
        if (loginBtn) {
            // Create a button container
            const buttonContainer = document.createElement('div');
            buttonContainer.style.cssText = `
                display: flex;
                gap: 10px;
                align-items: center;
            `;
            
            // Move login button to container
            loginBtn.parentNode.insertBefore(buttonContainer, loginBtn);
            buttonContainer.appendChild(loginBtn);
            buttonContainer.appendChild(signUpBtn);
        }
    }

    // 加载部门列表
    loadDepartments() {
        fetch('/api/departments/public')
            .then(response => response.json())
            .then(data => {
                this.departments = data;
            })
            .catch(error => {
                console.error('Error loading departments:', error);
                // Fallback departments
                this.departments = [
                    { id: 'CLT6', name: 'CLT6' },
                    { id: 'IT', name: 'IT' },
                    { id: 'HR', name: 'HR' },
                    { id: 'Finance', name: 'Finance' }
                ];
            });
    }

    // 显示注册弹窗
    showRegistrationModal() {
        const modal = this.createModal();
        document.body.appendChild(modal);
        
        // Focus on first input
        setTimeout(() => {
            const firstInput = modal.querySelector('input');
            if (firstInput) firstInput.focus();
        }, 100);
    }

    // 创建注册弹窗
    createModal() {
        const modal = document.createElement('div');
        modal.className = 'registration-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        `;

        const departmentOptions = this.departments.map(dept => 
            `<option value="${dept.id}">${dept.name}</option>`
        ).join('');

        modal.innerHTML = `
            <div style="
                background: white;
                padding: 30px;
                border-radius: 8px;
                width: 400px;
                max-width: 90vw;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            ">
                <h3 style="margin-top: 0; text-align: center; color: #333;">
                    ${this.t('user_registration') || 'User Registration'}
                </h3>
                
                <form id="registrationForm">
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                            ${this.t('badge_id') || 'Badge ID'} *
                        </label>
                        <input type="text" id="regBadgeId" required 
                               placeholder="${this.t('badge_placeholder') || 'Enter badge ID (min 5 digits)'}"
                               pattern="[0-9]{5,}" title="${this.t('badge_format') || 'Badge ID must be at least 5 digits'}"
                               style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                            ${this.t('full_name') || 'Full Name'} *
                        </label>
                        <input type="text" id="regFullName" required 
                               placeholder="${this.t('name_placeholder') || 'Enter your full name'}"
                               style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                            ${this.t('login_code') || 'Login Code'} *
                        </label>
                        <input type="text" id="regLoginCode" required 
                               placeholder="${this.t('login_placeholder') || 'Enter login code'}"
                               style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                            ${this.t('department') || 'Department'} *
                        </label>
                        <select id="regDepartment" required 
                                style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            <option value="">${this.t('select_department') || 'Select Department'}</option>
                            ${departmentOptions}
                        </select>
                    </div>
                    
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                            ${this.t('password') || 'Password'} (${this.t('optional') || 'Optional'})
                        </label>
                        <input type="password" id="regPassword" 
                               placeholder="${this.t('password_placeholder') || 'Leave empty for no password'}"
                               style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <small style="color: #666; font-size: 12px;">
                            ${this.t('password_hint') || 'Leave empty to login without password'}
                        </small>
                    </div>
                    
                    <div style="display: flex; gap: 10px; justify-content: flex-end;">
                        <button type="button" class="cancel-btn" style="
                            background: #6c757d;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 4px;
                            cursor: pointer;
                        ">${this.t('cancel') || 'Cancel'}</button>
                        
                        <button type="submit" class="register-btn" style="
                            background: #007bff;
                            color: white;
                            border: none;
                            padding: 10px 20px;
                            border-radius: 4px;
                            cursor: pointer;
                        ">${this.t('register') || 'Register'}</button>
                    </div>
                </form>
            </div>
        `;

        // Event listeners
        modal.querySelector('.cancel-btn').addEventListener('click', () => {
            modal.remove();
        });

        modal.querySelector('#registrationForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegistration(modal);
        });

        // Close on background click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        return modal;
    }

    // 处理注册
    handleRegistration(modal) {
        const formData = {
            badge_id: document.getElementById('regBadgeId').value.trim(),
            full_name: document.getElementById('regFullName').value.trim(),
            login_code: document.getElementById('regLoginCode').value.trim(),
            department: document.getElementById('regDepartment').value,
            password: document.getElementById('regPassword').value
        };

        // Validation
        if (!formData.badge_id || !formData.full_name || !formData.login_code || !formData.department) {
            this.showMessage(this.t('fill_required_fields') || 'Please fill in all required fields.', 'error');
            return;
        }

        // Badge ID validation
        if (!/^[0-9]{5,}$/.test(formData.badge_id)) {
            this.showMessage(this.t('invalid_badge_format') || 'Badge ID must be at least 5 digits.', 'error');
            return;
        }

        const registerBtn = modal.querySelector('.register-btn');
        registerBtn.disabled = true;
        registerBtn.textContent = this.t('registering') || 'Registering...';

        fetch('/api/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showMessage(
                    this.t('registration_success') || 'Registration successful! You can now login.',
                    'success'
                );
                modal.remove();
            } else {
                this.showMessage(data.error || (this.t('registration_failed') || 'Registration failed'), 'error');
                registerBtn.disabled = false;
                registerBtn.textContent = this.t('register') || 'Register';
            }
        })
        .catch(error => {
            console.error('Registration error:', error);
            this.showMessage(this.t('registration_failed') || 'Registration failed', 'error');
            registerBtn.disabled = false;
            registerBtn.textContent = this.t('register') || 'Register';
        });
    }

    // Translation helper
    t(key) {
        return window.i18n ? window.i18n.t(key) : null;
    }

    // Show message
    showMessage(message, type = 'info') {
        if (window.showMessage) {
            window.showMessage(message, type);
        } else {
            alert(message);
        }
    }

    // Destroy module
    destroy() {
        const signUpBtn = document.querySelector('.signup-btn');
        if (signUpBtn) {
            signUpBtn.remove();
        }
        this.isInitialized = false;
    }
}

// Export module
window.RegistrationModule = RegistrationModule;
