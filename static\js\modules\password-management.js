// 密码管理模块
class PasswordManagementModule {
    constructor() {
        this.isInitialized = false;
    }

    // 初始化模块
    init() {
        if (this.isInitialized) return;
        
        this.createPasswordModal();
        this.isInitialized = true;
    }

    // 创建密码修改模态框
    createPasswordModal() {
        const modal = document.createElement('div');
        modal.id = 'change-password-modal';
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3 data-i18n="change_password">${this.t('change_password') || '修改密码'}</h3>
                    <span class="close" onclick="closeChangePasswordModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="change-password-form">
                        <div class="form-group">
                            <label for="new-password" data-i18n="new_password">${this.t('new_password') || '新密码'}:</label>
                            <input type="password" id="new-password" name="new_password" 
                                   placeholder="${this.t('new_password_placeholder') || '留空表示去掉密码'}">
                            <small class="form-help" data-i18n="password_help">
                                ${this.t('password_help') || '留空表示去掉密码，不更改时不需要旧密码'}
                            </small>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn-primary" data-i18n="save_changes">
                                ${this.t('save_changes') || '保存更改'}
                            </button>
                            <button type="button" class="btn-secondary" onclick="closeChangePasswordModal()" data-i18n="cancel">
                                ${this.t('cancel') || '取消'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 设置表单提交事件
        const form = document.getElementById('change-password-form');
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.changePassword();
        });

        // 点击模态框外部关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeModal();
            }
        });
    }

    // 显示密码修改模态框
    showModal() {
        const modal = document.getElementById('change-password-modal');
        if (modal) {
            modal.style.display = 'block';
            document.getElementById('new-password').focus();
        }
    }

    // 关闭密码修改模态框
    closeModal() {
        const modal = document.getElementById('change-password-modal');
        if (modal) {
            modal.style.display = 'none';
            document.getElementById('change-password-form').reset();
        }
    }

    // 修改密码
    changePassword() {
        const newPassword = document.getElementById('new-password').value;

        fetch('/api/change-password', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                new_password: newPassword
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                this.showMessage(data.error, 'error');
            } else {
                this.showMessage(data.message || this.t('password_changed_success') || '密码修改成功', 'success');
                this.closeModal();
            }
        })
        .catch(error => {
            console.error('Error changing password:', error);
            this.showMessage(this.t('password_change_failed') || '密码修改失败', 'error');
        });
    }

    // Translation helper
    t(key) {
        return window.i18n ? window.i18n.t(key) : null;
    }

    // Show message
    showMessage(message, type = 'info') {
        // Create or update message element
        let messageEl = document.querySelector('.password-message');
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.className = 'password-message';
            messageEl.style.cssText = `
                margin: 10px 0;
                padding: 10px;
                border-radius: 4px;
                text-align: center;
            `;
            
            const form = document.getElementById('change-password-form');
            if (form) {
                form.appendChild(messageEl);
            }
        }
        
        messageEl.textContent = message;
        messageEl.className = `password-message ${type}`;
        
        // Style based on type
        if (type === 'error') {
            messageEl.style.background = '#f8d7da';
            messageEl.style.color = '#721c24';
            messageEl.style.border = '1px solid #f5c6cb';
        } else {
            messageEl.style.background = '#d4edda';
            messageEl.style.color = '#155724';
            messageEl.style.border = '1px solid #c3e6cb';
        }
        
        // Auto hide after 3 seconds for success messages
        if (type === 'success') {
            setTimeout(() => {
                if (messageEl.parentNode) {
                    messageEl.remove();
                }
            }, 3000);
        }
    }

    // Destroy module
    destroy() {
        const modal = document.getElementById('change-password-modal');
        if (modal) {
            modal.remove();
        }
        this.isInitialized = false;
    }
}

// Global functions
function showChangePasswordModal() {
    if (window.passwordManagementModule) {
        window.passwordManagementModule.showModal();
    }
}

function closeChangePasswordModal() {
    if (window.passwordManagementModule) {
        window.passwordManagementModule.closeModal();
    }
}

// Export module
window.PasswordManagementModule = PasswordManagementModule;
