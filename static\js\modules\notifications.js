// 通知模块
class NotificationsModule {
    constructor() {
        this.isInitialized = false;
        this.notificationInterval = null;
        this.problemSolveInterval = null;
    }

    // 初始化通知模块
    init() {
        if (this.isInitialized) return;
        
        this.setupNotificationBell();
        this.setupProblemSolveBadge();
        
        // 检查气泡
        this.checkNotifications();
        this.checkProblemSolveBadge();
        
        // 定时刷新气泡
        this.notificationInterval = setInterval(() => {
            this.checkNotifications();
        }, 60000);
        
        this.problemSolveInterval = setInterval(() => {
            this.checkProblemSolveBadge();
        }, 60000);
        
        this.isInitialized = true;
    }

    // 设置通知铃铛
    setupNotificationBell() {
        const bell = document.querySelector('.notification-bell');
        if (!bell) return;
        
        bell.addEventListener('click', () => {
            this.showNotifications();
        });
    }

    // 设置问题解决气泡
    setupProblemSolveBadge() {
        // 问题解决气泡的点击事件在HTML中已经设置
        // 这里只需要初始化检查
    }

    // 检查通知
    checkNotifications() {
        console.log('检查通知气泡...');
        fetch('/api/notifications')
            .then(response => {
                console.log('通知API响应状态:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('通知数据:', data);
                const unreadCount = data.filter(notification => !notification.read_status).length;
                console.log('未读通知数量:', unreadCount);
                const badge = document.getElementById('notificationBadge');
                if (badge) {
                    if (unreadCount > 0) {
                        badge.textContent = unreadCount;
                        badge.style.display = 'inline-block';
                        console.log('显示通知气泡:', unreadCount);
                    } else {
                        badge.style.display = 'none';
                        console.log('隐藏通知气泡');
                    }
                } else {
                    console.log('未找到通知气泡元素');
                }
            })
            .catch(error => {
                console.error('检查通知气泡时出错:', error);
            });
    }

    // 显示通知
    showNotifications() {
        console.log('显示通知列表...');
        
        // 立即清空气泡
        const badge = document.getElementById('notificationBadge');
        if (badge) {
            badge.style.display = 'none';
            console.log('点击铃铛时立即清空气泡');
        }
        
        fetch('/api/notifications')
            .then(response => response.json())
            .then(notifications => {
                this.displayNotifications(notifications);
            })
            .catch(error => {
                console.error('获取通知失败:', error);
                const errorMessage = window.i18n ?
                    window.i18n.t('fetch_notifications_failed') :
                    '获取通知失败';
                showMessage(errorMessage, 'error');
            });
    }

    // 显示通知列表
    displayNotifications(notifications) {
        const modal = document.createElement('div');
        modal.className = 'notifications-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        `;
        
        modal.innerHTML = `
            <div class="notifications-modal-content" style="
                background: white;
                padding: 30px;
                border-radius: 15px;
                width: 600px;
                max-width: 90vw;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            ">
                <div class="notifications-modal-header" style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                    border-bottom: 2px solid #f0f0f0;
                    padding-bottom: 15px;
                ">
                    <h3 style="margin: 0; color: #333;">🔔 ${window.i18n ? window.i18n.t('notification_list') : '通知列表'}</h3>
                    <button onclick="this.closest('.notifications-modal').remove()" style="
                        background: none;
                        border: none;
                        font-size: 24px;
                        cursor: pointer;
                        color: #666;
                    ">&times;</button>
                </div>
                <div class="notifications-list" style="max-height: 400px; overflow-y: auto;">
                    ${this.renderNotificationsList(notifications)}
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 点击外部关闭模态框
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.closeNotifications(notifications);
                modal.remove();
            }
        });
        
        // 点击关闭按钮时也标记已读
        const closeBtn = modal.querySelector('button');
        closeBtn.addEventListener('click', () => {
            this.closeNotifications(notifications);
        });
    }

    // 关闭通知中心时标记已读
    closeNotifications(notifications) {
        // 标记所有未读为已读
        const unread = notifications.filter(n => !n.read_status);
        if (unread.length > 0) {
            console.log('关闭通知浮窗，标记未读通知为已读:', unread.length, '条');
            console.log('未读通知详情:', unread);
            
            // 使用Promise.all确保所有API调用完成
            const markReadPromises = unread.map(n => {
                console.log(`开始标记通知 ${n.id} 为已读...`);
                return fetch(`/api/notifications/${n.id}/read`, { 
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => {
                    console.log(`通知 ${n.id} API响应状态:`, response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log(`通知 ${n.id} 标记已读成功:`, data);
                    // 验证返回的数据
                    if (data.success && data.notification) {
                        console.log(`通知 ${n.id} 更新后状态:`, data.notification.read_status);
                    }
                    return data;
                })
                .catch(error => {
                    console.error(`通知 ${n.id} 标记已读失败:`, error);
                    throw error;
                });
            });
            
            Promise.all(markReadPromises)
                .then((results) => {
                    console.log('所有通知标记已读完成，结果:', results);
                    // 显示成功消息
                    const message = window.i18n ?
                        `${unread.length} ${window.i18n.t('notifications_marked_read')}` :
                        `已标记 ${unread.length} 条通知为已读`;
                    showMessage(message, 'success');
                })
                .catch(error => {
                    console.error('标记通知已读时出错:', error);
                    const errorMessage = window.i18n ?
                        window.i18n.t('mark_read_failed') :
                        '标记已读失败，请重试';
                    showMessage(errorMessage, 'error');
                });
        } else {
            console.log('没有未读通知需要标记');
        }
    }

    // 渲染通知列表
    renderNotificationsList(notifications) {
        if (notifications.length === 0) {
            return `
                <div style="
                    text-align: center;
                    padding: 40px;
                    color: #666;
                    font-style: italic;
                ">
                    ${window.i18n ? window.i18n.t('no_notifications') : '暂无通知'}
                </div>
            `;
        }
        
        return notifications.map(notification => `
            <div class="notification-item" 
                 data-notification-id="${notification.id}"
                 style="
                    padding: 15px;
                    border-bottom: 1px solid #f0f0f0;
                    cursor: pointer;
                    transition: background-color 0.2s;
                    ${!notification.read_status ? 'background-color: #f8f9fa;' : ''}
                 "
                 onmouseover="this.style.backgroundColor='#f0f0f0'"
                 onmouseout="this.style.backgroundColor='${!notification.read_status ? '#f8f9fa' : 'white'}'"
            >
                <div style="
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-start;
                    gap: 10px;
                ">
                    <div style="flex: 1;">
                        <div style="
                            font-weight: ${!notification.read_status ? '600' : '400'};
                            color: #333;
                            margin-bottom: 5px;
                        ">
                            ${notification.title}
                        </div>
                        <div style="
                            color: #666;
                            font-size: 0.9em;
                            line-height: 1.4;
                        ">
                            ${notification.content}
                        </div>
                        <div style="
                            color: #999;
                            font-size: 0.8em;
                            margin-top: 8px;
                        ">
                            ${this.formatDate(notification.created_at)}
                        </div>
                    </div>
                    ${!notification.read_status ? `
                        <div style="
                            width: 8px;
                            height: 8px;
                            background-color: #007bff;
                            border-radius: 50%;
                            flex-shrink: 0;
                            margin-top: 5px;
                        "></div>
                    ` : ''}
                </div>
            </div>
        `).join('');
    }

    // 标记通知为已读
    markNotificationRead(notificationId, element) {
        fetch(`/api/notifications/${notificationId}/read`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                element.style.background = '#f8f9fa';
                this.checkNotifications(); // 重新检查通知数量
            }
        })
        .catch(error => {
            console.error('Error marking notification read:', error);
        });
    }

    // 检查问题解决气泡
    checkProblemSolveBadge() {
        console.log('检查Problem Solve气泡...');
        fetch('/api/problem-solves')
            .then(response => {
                console.log('Problem Solve API响应状态:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Problem Solve数据:', data);
                const unreplyCount = data.filter(item => !item.reply).length;
                console.log('未回复数量:', unreplyCount);
                const badge = document.getElementById('psBadge');
                if (badge) {
                    if (unreplyCount > 0) {
                        badge.textContent = unreplyCount;
                        badge.style.display = 'inline-block';
                        console.log('显示Problem Solve气泡:', unreplyCount);
                    } else {
                        badge.style.display = 'none';
                        console.log('隐藏Problem Solve气泡');
                    }
                } else {
                    console.log('未找到Problem Solve气泡元素');
                }
            })
            .catch(error => {
                console.error('检查Problem Solve气泡时出错:', error);
            });
    }

    // 格式化日期
    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        
        // 小于1分钟
        if (diff < 60000) {
            return '刚刚';
        }
        
        // 小于1小时
        if (diff < 3600000) {
            return `${Math.floor(diff / 60000)}分钟前`;
        }
        
        // 小于24小时
        if (diff < 86400000) {
            return `${Math.floor(diff / 3600000)}小时前`;
        }
        
        // 小于7天
        if (diff < 604800000) {
            return `${Math.floor(diff / 86400000)}天前`;
        }
        
        // 超过7天，显示具体日期
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    // 销毁模块
    destroy() {
        if (this.notificationInterval) {
            clearInterval(this.notificationInterval);
        }
        if (this.problemSolveInterval) {
            clearInterval(this.problemSolveInterval);
        }
        this.isInitialized = false;
    }
}

// 导出通知模块
window.NotificationsModule = NotificationsModule; 