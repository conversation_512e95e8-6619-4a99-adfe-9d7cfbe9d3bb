/**
 * 后台反馈管理模块
 * 处理后台反馈查看、回复、删除等功能
 */
class AdminFeedbackModule {
    constructor() {
        this.init();
    }

    init() {
        console.log('初始化后台反馈管理模块...');
        this.setupFeedbackBadge();
        this.startBadgeCheck();
    }

    // 设置反馈气泡
    setupFeedbackBadge() {
        const badge = document.getElementById('feedbackBadge');
        if (badge) {
            console.log('找到反馈气泡元素');
        } else {
            console.log('未找到反馈气泡元素');
        }
    }

    // 开始定时检查未回复反馈数量
    startBadgeCheck() {
        console.log('开始定时检查未回复反馈数量...');
        // 立即检查一次
        this.checkFeedbackBadge();
        // 每分钟检查一次
        setInterval(() => this.checkFeedbackBadge(), 60000);
    }

    // 检查未回复反馈数量
    checkFeedbackBadge() {
        console.log('检查未回复反馈数量...');
        fetch('/api/feedback/unreplied-count')
            .then(response => {
                console.log('未回复反馈API响应状态:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('未回复反馈数量:', data.count);
                const badge = document.getElementById('feedbackBadge');
                if (badge) {
                    badge.textContent = data.count;
                    badge.style.display = data.count > 0 ? 'inline' : 'none';
                    console.log(`反馈气泡${data.count > 0 ? '显示' : '隐藏'}: ${data.count}条未回复`);
                } else {
                    console.log('未找到反馈气泡元素');
                }
            })
            .catch(error => {
                console.error('检查未回复反馈数量时出错:', error);
            });
    }

    // 加载反馈列表
    loadFeedback() {
        console.log('加载反馈列表...');
        if (!this.checkPermission('can_view_feedback')) {
            this.showNoPermission('feedback', 3, '反馈查看');
            return;
        }

        const tbody = document.querySelector('#feedback-table tbody');
        if (!tbody) {
            console.log('未找到反馈表格tbody元素');
            return;
        }

        tbody.innerHTML = '<tr><td colspan="4" style="text-align: center;">加载中...</td></tr>';

        fetch('/api/feedback')
            .then(response => response.json())
            .then(feedback => {
                console.log('获取到反馈数据:', feedback.length, '条');
                if (feedback.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="4" style="text-align: center;">暂无反馈</td></tr>';
                    return;
                }

                tbody.innerHTML = feedback.map(item => {
                    const actionButtons = [];
                    
                    if (this.checkPermission('can_reply_feedback')) {
                        actionButtons.push(`<button class="btn-edit" onclick="adminFeedbackModule.replyToFeedback(${item.id})">回复</button>`);
                    }
                    
                    if (this.checkPermission('can_delete_feedback')) {
                        actionButtons.push(`<button class="btn-delete" onclick="adminFeedbackModule.deleteFeedback(${item.id})">删除</button>`);
                    }

                    return `
                        <tr>
                            <td>${this.escapeHTML(item.user_name || '未知用户')}</td>
                            <td>${this.escapeHTML(item.title || '无标题')}</td>
                            <td>${this.escapeHTML(item.content)}</td>
                            <td>${item.reply ? this.escapeHTML(item.reply) : '<span style="color: #dc3545;">未回复</span>'}</td>
                            <td>
                                ${actionButtons.join('')}
                            </td>
                        </tr>
                    `;
                }).join('');
            })
            .catch(error => {
                console.error('加载反馈列表失败:', error);
                tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; color: red;">加载失败</td></tr>';
            });
    }

    // 回复反馈
    replyToFeedback(id) {
        console.log('回复反馈:', id);
        if (!this.checkPermission('can_reply_feedback')) {
            this.showMessage('没有回复权限', 'error');
            return;
        }

        fetch('/api/feedback')
            .then(response => response.json())
            .then(feedback => {
                const item = feedback.find(f => f.id === id);
                if (!item) {
                    this.showMessage('反馈不存在', 'error');
                    return;
                }
                
                const modal = this.createModal('回复反馈', `
                    <div class="form-group">
                        <label>原反馈内容：</label>
                        <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-bottom: 15px;">
                            <strong>标题：</strong>${this.escapeHTML(item.title || '无标题')}<br>
                            <strong>内容：</strong>${this.escapeHTML(item.content)}
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="feedback-reply">回复内容</label>
                        <textarea id="feedback-reply" rows="4" required placeholder="请输入回复内容...">${this.escapeHTML(item.reply || '')}</textarea>
                    </div>
                `);
                
                modal.querySelector('.btn-save').onclick = () => {
                    const reply = document.getElementById('feedback-reply').value.trim();
                    if (!reply) {
                        alert('请填写回复内容');
                        return;
                    }
                    this.submitFeedbackReply(id, { reply });
                    this.closeModal(modal);
                };
            })
            .catch(error => {
                console.error('加载反馈详情失败:', error);
                this.showMessage('加载反馈失败', 'error');
            });
    }

    // 提交反馈回复
    submitFeedbackReply(id, replyData) {
        console.log('提交反馈回复:', id, replyData);
        fetch(`/api/feedback/${id}/reply`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(replyData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showMessage('回复成功', 'success');
                this.loadFeedback();
                // 重新检查未回复数量
                this.checkFeedbackBadge();
            } else {
                this.showMessage(data.error || '回复失败', 'error');
            }
        })
        .catch(error => {
            console.error('回复反馈失败:', error);
            this.showMessage('回复失败', 'error');
        });
    }

    // 删除反馈
    deleteFeedback(id) {
        console.log('删除反馈:', id);
        if (!this.checkPermission('can_delete_feedback')) {
            this.showMessage('没有删除权限', 'error');
            return;
        }

        if (!confirm('确定要删除这个反馈吗？')) return;
        
        fetch(`/api/feedback/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showMessage('删除成功', 'success');
                this.loadFeedback();
                // 重新检查未回复数量
                this.checkFeedbackBadge();
            } else {
                this.showMessage(data.error || '删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('删除反馈失败:', error);
            this.showMessage('删除失败', 'error');
        });
    }

    // 权限检查
    checkPermission(permissionName) {
        if (window.userPermissions && window.userPermissions[permissionName]) {
            return true;
        }
        return false;
    }

    // 显示无权限提示
    showNoPermission(sectionId, colCount = 3, moduleName = '') {
        const section = document.getElementById(sectionId);
        if (section) {
            section.innerHTML = `
                <div style="text-align: center; padding: 50px; color: #666;">
                    <h3>🔒 权限不足</h3>
                    <p>您没有${moduleName}的权限，请联系管理员。</p>
                </div>
            `;
        }
    }

    // 创建模态框
    createModal(title, content, modalId, onSave) {
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        `;
        
        modal.innerHTML = `
            <div class="modal-content" style="
                background: white;
                padding: 30px;
                border-radius: 15px;
                width: 500px;
                max-width: 90vw;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            ">
                <div class="modal-header" style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                    border-bottom: 2px solid #f0f0f0;
                    padding-bottom: 15px;
                ">
                    <h3 style="margin: 0; color: #333;">${title}</h3>
                    <button onclick="this.closest('.modal').remove()" style="
                        background: none;
                        border: none;
                        font-size: 24px;
                        cursor: pointer;
                        color: #666;
                    ">&times;</button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="modal-footer" style="
                    display: flex;
                    gap: 10px;
                    justify-content: flex-end;
                    margin-top: 20px;
                    border-top: 1px solid #f0f0f0;
                    padding-top: 15px;
                ">
                    <button class="btn-cancel" onclick="this.closest('.modal').remove()" style="
                        padding: 10px 20px;
                        border: 1px solid #ddd;
                        background: #f8f9fa;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                        font-weight: 500;
                        color: #666;
                        transition: all 0.2s ease;
                        min-width: 80px;
                    " onmouseover="this.style.backgroundColor='#e9ecef'; this.style.borderColor='#adb5bd';" 
                       onmouseout="this.style.backgroundColor='#f8f9fa'; this.style.borderColor='#ddd';">取消</button>
                    <button class="btn-save" style="
                        padding: 10px 20px;
                        background: linear-gradient(45deg, #667eea, #764ba2);
                        color: white;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-weight: 600;
                        font-size: 14px;
                        transition: all 0.2s ease;
                        min-width: 80px;
                    " onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(0,0,0,0.2)'" 
                       onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none';">保存</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // 点击外部关闭模态框
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
        
        return modal;
    }

    // 关闭模态框
    closeModal(modal) {
        if (modal && modal.parentNode) {
            modal.remove();
        }
    }

    // 显示消息
    showMessage(message, type = 'info') {
        if (window.showMessage) {
            showMessage(message, type);
        } else {
            alert(message);
        }
    }

    // HTML转义
    escapeHTML(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 销毁模块
    destroy() {
        console.log('销毁后台反馈管理模块...');
        // 清理定时器等资源
    }
}

// 导出模块
window.AdminFeedbackModule = AdminFeedbackModule; 