document.addEventListener('DOMContentLoaded', function() {
    // Global variables
    let currentLabels = [];
    let currentDepartments = [];
    
    // Initialize utility module first
    const utilsModule = new UtilsModule();
    utilsModule.init();
    
    // Initialize the page
    initializePage();
    
    // Admin page navigation (if on admin page)
    initializeAdminNavigation();
    
    // Initialize search functionality using the new module
    const searchModule = new SearchModule();
    searchModule.init();
    
    // Initialize notifications functionality using the new module
    const notificationsModule = new NotificationsModule();
    notificationsModule.init();
    
    // Initialize announcements functionality using the new module
    const announcementsModule = new AnnouncementsModule();
    announcementsModule.init();
    
    function initializePage() {
        console.log('初始化页面...');
        
        // 初始化各个模块
        if (window.SearchModule) {
            window.searchModule = new SearchModule();
        }
        
        if (window.UtilsModule) {
            window.utilsModule = new UtilsModule();
        }
        
        if (window.NotificationsModule) {
            window.notificationsModule = new NotificationsModule();
        }
        
        if (window.FeedbackModule) {
            window.feedbackModule = new FeedbackModule();
        }
        
        // 加载标签和部门数据
        loadLabels();
        loadDepartments();
        
        // 设置快速打印功能
        setupQuickPrint();
        
        // 初始化管理员导航（如果存在）
        initializeAdminNavigation();
        
        // 自动选择当前用户部门
        if (window.userInfo && window.userInfo.department) {
            const deptSelect = document.getElementById('department-select');
            if (deptSelect) {
                for (let i = 0; i < deptSelect.options.length; i++) {
                    if (deptSelect.options[i].text === window.userInfo.department) {
                        deptSelect.selectedIndex = i;
                        break;
                    }
                }
            }
        }
        
        console.log('页面初始化完成');
    }
    
    function loadLabels() {
        console.log('开始加载标签...');
        fetch('/api/labels')
            .then(response => {
                console.log('API响应状态:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(labels => {
                console.log('标签数据:', labels);
                console.log('标签数量:', labels.length);
                currentLabels = labels;
                filterLabelsByDepartment(labels);
            })
            .catch(error => {
                console.error('加载标签时出错:', error);
                showMessage('加载标签失败: ' + error.message, 'error');
            });
    }
    
    function filterLabelsByDepartment(labels) {
        const departmentSelect = document.getElementById('department-select');
        if (!departmentSelect) {
            renderLabelGrid(labels);
            return;
        }
        
        const selectedDepartment = departmentSelect.options[departmentSelect.selectedIndex];
        if (!selectedDepartment) {
            renderLabelGrid(labels);
            return;
        }
        
        const currentDepartmentName = selectedDepartment.textContent;
        console.log('当前选择的部门:', currentDepartmentName);
        
        // 过滤标签：只显示属于当前部门的标签
        const filteredLabels = labels.filter(label => {
            if (!label.department_names || label.department_names.trim() === '') {
                // 如果标签没有设置部门，则显示给所有部门
                return true;
            }
            
            const labelDepartments = label.department_names.split(',').map(dept => dept.trim());
            return labelDepartments.includes(currentDepartmentName);
        });
        
        console.log(`过滤后的标签数量: ${filteredLabels.length}/${labels.length}`);
        renderLabelGrid(filteredLabels);
    }
    
    function loadDepartments() {
        fetch('/api/departments')
            .then(response => response.json())
            .then(departments => {
                currentDepartments = departments;
                renderDepartmentSelect(departments);
            })
            .catch(error => {
                console.error('Error loading departments:', error);
            });
    }
    
    function renderLabelGrid(labels) {
        // 没有标签操作权限，直接提示
        if (!checkFrontPermission('can_print_labels')) {
            const grid = document.querySelector('.label-grid');
            if (grid) {
                grid.innerHTML = '<p style="grid-column: 1 / -1; text-align: center; color: #666;">无标签操作权限</p>';
            }
            return;
        }
        console.log('开始渲染标签网格...');
        console.log('传入的标签数据:', labels);
        
        const grid = document.querySelector('.label-grid');
        console.log('找到的网格元素:', grid);
        
        if (!grid) {
            console.error('未找到标签网格元素!');
            return;
        }
        
        grid.innerHTML = '';
        
        if (labels.length === 0) {
            console.log('没有标签数据，显示空状态');
            grid.innerHTML = '<p style="grid-column: 1 / -1; text-align: center; color: #666;">暂无标签</p>';
            return;
        }
        
        console.log(`开始渲染 ${labels.length} 个标签`);
        labels.forEach((label, index) => {
            console.log(`渲染标签 ${index + 1}:`, label);
            const button = document.createElement('button');
            button.textContent = label.name;
            button.className = `label-btn label-${label.type}`;
            button.dataset.labelId = label.id;
            button.dataset.printPrompt = label.print_prompt;
            
            button.addEventListener('click', () => handleLabelClick(label));
            grid.appendChild(button);
        });
        
        console.log('标签网格渲染完成');
    }
    
    function renderDepartmentSelect(departments) {
        const select = document.getElementById('department-select');
        if (!select) return;
        
        select.innerHTML = '';
        
        departments.forEach(dept => {
            const option = document.createElement('option');
            option.value = dept.id;
            option.textContent = dept.name;
            select.appendChild(option);
        });
        
        // Set current user's department as selected
        const userInfo = document.querySelector('.user-info');
        if (userInfo) {
            const userDept = userInfo.textContent.match(/\(([^)]+)\)/);
            if (userDept) {
                const deptName = userDept[1];
                const deptOption = Array.from(select.options).find(opt => opt.textContent === deptName);
                if (deptOption) {
                    deptOption.selected = true;
                }
            }
        }
        
        // 添加部门切换事件监听器
        select.addEventListener('change', function() {
            console.log('部门切换为:', this.options[this.selectedIndex].textContent);
            filterLabelsByDepartment(currentLabels);
        });
    }
    
    function setupQuickPrint() {
        // Removed old quick-print functionality
        // Now using new dropdown search instead
    }
    
    function handleLabelClick(label) {
        // 没有打印权限禁止打印
        if (!checkFrontPermission('can_print_labels')) {
            showMessage('无标签打印权限', 'error');
            return;
        }

        // 只有有复制权限时才允许复制
        if (label.copy_info && label.copy_info.trim()) {
            if (checkFrontPermission('can_copy_labels')) {
                copyToClipboard(label.copy_info);
            } else {
                showMessage('无标签复制权限', 'error');
            }
        }

        // 根据标签设置决定打印行为
        if (label.print_prompt) {
            // 有打印数量提示功能：让用户输入数量
            const defaultQuantity = label.print_quantity || 1;
            const quantity = prompt(`请输入要打印的 ${label.name} 数量:`, defaultQuantity.toString());
            if (quantity === null) return; // 用户取消

            const num = parseInt(quantity);
            if (isNaN(num) || num < 1) {
                alert('请输入有效的数量');
                return;
            }

            printLabel(label.id, num);
        } else {
            // 没有打印数量提示功能：直接使用标签设置的打印数量
            const quantity = label.print_quantity || 1;
            printLabel(label.id, quantity);
        }

        // 如果是problem solve标签，打印后显示问题解决表单
        if (label.problem_solve) {
            showProblemSolveForm(label);
        }
    }
    
    function showProblemSolveForm(label) {
        // Create modal for problem solve form
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        `;
        
        modal.innerHTML = `
            <div class="modal-content" style="
                background: white;
                padding: 30px;
                border-radius: 10px;
                width: 500px;
                max-width: 90vw;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            ">
                <div class="modal-header" style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 20px;
                ">
                    <h3 style="margin: 0; color: #333;">问题反馈 - ${label.name}</h3>
                    <button class="close" onclick="this.closest('.modal').remove()" style="
                        background: none;
                        border: none;
                        font-size: 24px;
                        cursor: pointer;
                        color: #666;
                    ">&times;</button>
                </div>
                <form id="problemSolveForm">
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">问题标题：</label>
                        <input type="text" name="title" required style="width: 100%; padding: 10px; border: 2px solid #e1e5e9; border-radius: 6px; font-family: inherit; box-sizing: border-box;" placeholder="请简要填写问题标题...">
                    </div>
                    <div class="form-group" style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #333;">请描述您遇到的问题：</label>
                        <textarea 
                            name="problem" 
                            required 
                            rows="5" 
                            style="width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 6px; font-family: inherit; resize: vertical; box-sizing: border-box;"
                            placeholder="请详细描述您遇到的问题..."
                        ></textarea>
                    </div>
                    <div class="modal-actions" style="display: flex; gap: 10px; justify-content: flex-end;">
                        <button type="button" onclick="this.closest('.modal').remove()" style="padding: 10px 20px; border: 1px solid #ddd; background: #f8f9fa; border-radius: 6px; cursor: pointer;">取消</button>
                        <button type="submit" style="padding: 10px 20px; background: linear-gradient(45deg, #667eea, #764ba2); color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: 600;">提交问题</button>
                    </div>
                </form>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Handle form submission
        const form = modal.querySelector('#problemSolveForm');
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            const title = form.querySelector('input[name="title"]').value.trim();
            const problemContent = form.querySelector('textarea[name="problem"]').value.trim();
            if (!title) {
                alert('请填写问题标题');
                return;
            }
            if (!problemContent) {
                alert('请填写问题描述');
                return;
            }
            // Submit problem solve
            submitProblemSolve(label.id, title, problemContent, modal);
        });
        
        // Close modal when clicking outside
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }
    
    function submitProblemSolve(labelId, title, content, modal) {
        fetch('/api/problem-solves', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                label_id: labelId,
                title: title,
                content: content
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('问题提交成功！', 'success');
                modal.remove();
            } else {
                showMessage('提交失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error submitting problem solve:', error);
            showMessage('提交失败，请重试', 'error');
        });
    }
    
    function printLabel(labelId, quantity) {
        if (!checkFrontPermission('can_print_labels')) {
            showMessage('无标签打印权限', 'error');
            return;
        }

        // 检查是否在桌面客户端中运行
        if (window.pywebview && window.pywebview.api) {
            // 在桌面客户端中，直接调用静默打印
            console.log('检测到桌面客户端，执行静默打印');
            performDesktopSilentPrint(labelId, quantity);
        } else {
            // 在浏览器中，使用原来的打印方式
            console.log('在浏览器中运行，使用传统打印方式');
            performBrowserPrint(labelId, quantity);
        }
    }

    function performDesktopSilentPrint(labelId, quantity) {
        console.log(`开始静默打印标签 ${labelId}，数量: ${quantity}`);

        // 显示打印中通知
        showPrintNotification('Printing...', 'info');

        // 获取标签内容
        fetch(`/api/print-label/${labelId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                quantity: quantity || 1
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.print_content) {
                console.log('获取到打印内容，发送到桌面客户端');

                // 创建打印数据
                const printData = {
                    content: data.print_content,
                    labelId: labelId,
                    quantity: quantity,
                    timestamp: new Date().toISOString(),
                    action: 'silent_print'
                };

                // 直接调用桌面客户端的静默打印API
                window.pywebview.api.handle_print_request(JSON.stringify(printData))
                    .then(result => {
                        console.log('静默打印结果:', result);
                        if (result && result.success) {
                            showPrintNotification('Print Successful', 'success');
                        } else {
                            showPrintNotification('Print Failed: ' + (result.message || 'Unknown Error'), 'error');
                        }
                    })
                    .catch(error => {
                        console.error('静默打印失败:', error);
                        showPrintNotification('Print Failed', 'error');
                    });
            } else {
                showPrintNotification('Failed to Get Print Content', 'error');
            }
        })
        .catch(error => {
            console.error('获取打印内容失败:', error);
            showPrintNotification('Failed to Get Print Content', 'error');
        });
    }

    function performBrowserPrint(labelId, quantity) {
        // 原来的浏览器打印逻辑
        const requestBody = {
            quantity: quantity || 1
        };

        fetch(`/api/print-label/${labelId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');

                // 在浏览器中打开打印窗口
                if (data.print_content) {
                    openPrintWindow(data.print_content);
                }
            } else {
                showMessage('打印失败: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error printing label:', error);
            showMessage('打印失败，请重试', 'error');
        });
    }

    function showPrintNotification(message, type) {
        const notification = document.createElement('div');
        const bgColor = type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8';

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${bgColor};
            color: white;
            padding: 12px 20px;
            border-radius: 5px;
            z-index: 10000;
            font-size: 14px;
            font-family: Arial, sans-serif;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    function openPrintWindow(htmlContent) {
        try {
            // 创建新窗口用于打印
            const printWindow = window.open('', '_blank', 'width=900,height=700,scrollbars=yes,resizable=yes');

            if (printWindow) {
                // 写入HTML内容
                printWindow.document.write(htmlContent);
                printWindow.document.close();

                // 等待内容加载完成后自动打印
                printWindow.onload = function() {
                    setTimeout(() => {
                        // 自动触发打印对话框
                        printWindow.print();

                        // 打印完成后关闭窗口（可选）
                        // printWindow.onafterprint = function() {
                        //     printWindow.close();
                        // };
                    }, 800); // 增加延迟确保CSS加载完成
                };

                // 处理窗口关闭事件
                printWindow.onbeforeunload = function() {
                    console.log('打印窗口已关闭');
                };

            } else {
                // 如果弹窗被阻止，提示用户并提供备选方案
                showMessage('浏览器阻止了弹窗，请允许弹窗后重试，或使用备选打印方式', 'warning');

                // 备选方案：在当前页面打印
                if (confirm('浏览器阻止了打印窗口，是否在当前页面打印？（打印后页面会刷新）')) {
                    printInCurrentWindow(htmlContent);
                }
            }
        } catch (error) {
            console.error('打印窗口创建失败:', error);
            showMessage('打印窗口创建失败，尝试备选方案', 'error');
            printInCurrentWindow(htmlContent);
        }
    }

    function printInCurrentWindow(htmlContent) {
        // 保存当前页面内容
        const originalContent = document.body.innerHTML;
        const originalTitle = document.title;

        try {
            // 替换页面内容
            document.body.innerHTML = htmlContent;
            document.title = '标签打印';

            // 触发打印
            window.print();

        } finally {
            // 恢复原始内容
            setTimeout(() => {
                document.body.innerHTML = originalContent;
                document.title = originalTitle;
                // 重新初始化页面功能
                location.reload();
            }, 1000);
        }
    }


    
    function initializeAdminNavigation() {
        const adminNav = document.querySelector('.admin-nav');
        if (!adminNav) return;
        
        const navItems = adminNav.querySelectorAll('ul li a');
        navItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all items
                navItems.forEach(nav => nav.classList.remove('active'));
                
                // Add active class to clicked item
                this.classList.add('active');
                
                // Show corresponding content
                const target = this.getAttribute('data-target');
                showAdminContent(target);
            });
        });
        
        // Show first tab by default
        if (navItems.length > 0) {
            navItems[0].click();
        }
    }
    
    function showAdminContent(target) {
        const sections = document.querySelectorAll('.admin-content section');
        sections.forEach(section => {
            section.style.display = section.id === target ? 'block' : 'none';
        });
        
        // Load data for the selected section
        loadAdminData(target);
    }
    
    function loadAdminData(section) {
        switch(section) {
            case 'labels':
                loadAdminLabels();
                break;
            case 'users':
                loadAdminUsers();
                break;
            case 'departments':
                loadAdminDepartments();
                break;
            case 'user-groups':
                loadAdminUserGroups();
                break;
            case 'announcements':
                loadAdminAnnouncements();
                break;
            case 'feedback':
                loadAdminFeedback();
                break;
            case 'problem-solves':
                loadAdminProblemSolves();
                break;
            case 'barcode-prefixes':
                loadAdminBarcodePrefixes();
                break;
            case 'chatbot-kb':
                loadAdminChatbotKB();
                break;
            case 'audit-log':
                loadAdminAuditLog();
                break;
        }
    }
    
    // Admin data loading functions (placeholder implementations)
    function loadAdminLabels() {
        console.log('Loading admin labels...');
    }
    
    function loadAdminUsers() {
        console.log('Loading admin users...');
    }
    
    function loadAdminDepartments() {
        console.log('Loading admin departments...');
    }
    
    function loadAdminUserGroups() {
        console.log('Loading admin user groups...');
    }
    
    function loadAdminAnnouncements() {
        console.log('Loading admin announcements...');
    }
    
    function loadAdminFeedback() {
        console.log('Loading admin feedback...');
    }
    
    function loadAdminProblemSolves() {
        console.log('Loading admin problem solves...');
    }
    
    function loadAdminBarcodePrefixes() {
        console.log('Loading admin barcode prefixes...');
    }
    
    function loadAdminChatbotKB() {
        console.log('Loading admin chatbot KB...');
    }
    
    function loadAdminAuditLog() {
        console.log('Loading admin audit log...');
    }
    
    // 语言切换功能
    function initializeLanguageDropdown() {
        // 为语言选项添加点击事件
        document.querySelectorAll('.language-option').forEach(option => {
            option.addEventListener('click', function() {
                const lang = this.dataset.lang;
                if (lang) {
                    // 保存语言设置到服务器
                    fetch('/api/user-language', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ language: lang })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 刷新页面以应用新语言
                            window.location.reload();
                        } else {
                            console.error('Failed to save language setting');
                        }
                    })
                    .catch(error => {
                        console.error('Error saving language:', error);
                    });
                }
            });
        });

        // 更新当前语言高亮
        function updateActiveLanguage(currentLang) {
            document.querySelectorAll('.language-option').forEach(option => {
                option.classList.remove('active');
                if (option.dataset.lang === currentLang) {
                    option.classList.add('active');
                }
            });
        }

        // 获取当前用户语言设置并高亮显示
        if (window.i18n) {
            updateActiveLanguage(window.i18n.currentLanguage);
        }
    }

    // 初始化语言下拉菜单
    initializeLanguageDropdown();

    // 将函数暴露到全局作用域，供搜索模块使用
    window.handleLabelClick = handleLabelClick;
    window.showProblemSolveForm = showProblemSolveForm;
    window.submitProblemSolve = submitProblemSolve;
    window.printLabel = printLabel;
});