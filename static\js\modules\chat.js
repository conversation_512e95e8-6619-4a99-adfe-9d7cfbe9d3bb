/**
 * 主页聊天模块
 */
class ChatModule {
    constructor() {
        this.chatWindow = null;
        this.isChatOpen = false;
        this.mode = 'robot'; // 'robot' or 'public'
        this.kbList = [];
        this.unreadCount = 0;
        this.hasMentions = false;
        this.firstMentionId = null;
        this.lastMessageTimestamp = null;
        this.publicChatPollInterval = null;
        this.mentionCheckInterval = null;
        this.init();
    }

    init() {
        this.floatBtn = document.querySelector('.chat-float');
        if (!this.floatBtn) return;
        this.createNotificationBubble();
        this.createChatWindow();
        this.floatBtn.addEventListener('click', () => this.openChat());
        this.updateModeButtonsVisibility();
        this.startMentionCheck();
    }

    // 检查用户权限
    hasPermission(permission) {
        return window.userPermissions && window.userPermissions[permission];
    }

    // 更新模式按钮的可见性（保持所有按钮显示）
    updateModeButtonsVisibility() {
        // 所有模式按钮都保持显示，权限检查在切换模式时进行
        // 不隐藏任何按钮，让用户可以看到所有模式选项
    }

    // 显示特定模式的无权限提示
    showNoPermissionForMode(modeName) {
        const messagesContainer = this.chatWindow.querySelector('.chat-messages');
        if (messagesContainer) {
            messagesContainer.innerHTML = `
                <div style="
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: center;
                    height: 100%;
                    color: #666;
                    text-align: center;
                    padding: 20px;
                ">
                    <div style="font-size: 48px; margin-bottom: 16px;">🔒</div>
                    <div style="font-size: 16px; font-weight: 500; margin-bottom: 8px;">No Permission</div>
                    <div style="font-size: 14px; color: #999;">Please Contact Admin For The Permission</div>
                </div>
            `;
        }
    }

    // 锁定输入区域
    lockInputArea() {
        const inputArea = this.chatWindow.querySelector('.chat-input-area');
        const chatInput = this.chatWindow.querySelector('#chat-input');
        const sendBtn = this.chatWindow.querySelector('#chat-send-btn');

        if (inputArea) {
            inputArea.style.position = 'relative';

            // 创建锁定覆盖层
            let lockOverlay = inputArea.querySelector('.input-lock-overlay');
            if (!lockOverlay) {
                lockOverlay = document.createElement('div');
                lockOverlay.className = 'input-lock-overlay';
                lockOverlay.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.1);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #999;
                    font-size: 14px;
                    z-index: 10;
                    border-radius: 10px;
                `;
                lockOverlay.innerHTML = '🔒 No Permission To Send Message';
                inputArea.appendChild(lockOverlay);
            }

            if (chatInput) chatInput.disabled = true;
            if (sendBtn) sendBtn.disabled = true;
        }
    }

    // 解锁输入区域
    unlockInputArea() {
        const inputArea = this.chatWindow.querySelector('.chat-input-area');
        const chatInput = this.chatWindow.querySelector('#chat-input');
        const sendBtn = this.chatWindow.querySelector('#chat-send-btn');

        if (inputArea) {
            const lockOverlay = inputArea.querySelector('.input-lock-overlay');
            if (lockOverlay) {
                lockOverlay.remove();
            }

            if (chatInput) chatInput.disabled = false;
            if (sendBtn) sendBtn.disabled = false;
        }
    }

    createNotificationBubble() {
        this.bubble = this.floatBtn.querySelector('.chat-notification-bubble') || document.createElement('span');
        this.bubble.className = 'chat-notification-bubble';
        this.bubble.style.cssText = `
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            min-width: 20px;
            height: 20px;
            display: none;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            z-index: 10;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        `;
        this.floatBtn.appendChild(this.bubble);
    }

    updateBubble(count, hasMentions = false) {
        this.unreadCount = count;
        this.hasMentions = hasMentions;

        if (hasMentions) {
            // 如果有@消息，显示@符号
            this.bubble.textContent = '@';
            this.bubble.style.display = 'flex';
            this.bubble.style.background = '#ff6b35'; // 橙色表示@消息
        } else if (count > 0) {
            // 如果有普通未读消息，显示数字
            this.bubble.textContent = count > 99 ? '99+' : count;
            this.bubble.style.display = 'flex';
            this.bubble.style.background = '#ff4757'; // 红色表示普通消息
        } else {
            this.bubble.style.display = 'none';
        }
    }

    createChatWindow() {
        this.chatWindow = document.createElement('div');
        this.chatWindow.id = 'chat-window';
        this.chatWindow.className = 'chat-window';
        this.chatWindow.style.display = 'none';
        this.chatWindow.innerHTML = `
            <div class="chat-header">
                <h3>Chat</h3>
                <div class="chat-mode-switcher">
                    <button class="chat-mode-btn active" data-mode="robot">Robot</button>
                    <button class="chat-mode-btn" data-mode="public">Public</button>
                </div>
                <button class="chat-close-btn">&times;</button>
            </div>
            <div class="chat-messages"></div>
            <div class="chat-input-area">
                <div class="chat-input-container">
                    <input type="text" id="chat-input" placeholder="Type In Message...">
                    <div class="user-suggestions" id="user-suggestions" style="display: none;"></div>
                </div>
                <button id="chat-send-btn" title="发送">
                  <span style="font-size:20px;font-weight:bold;display:inline-block;transform:rotate(45deg);">↑</span>
                </button>
            </div>
        `;
        document.body.appendChild(this.chatWindow);

        this.chatWindow.querySelector('.chat-close-btn').onclick = () => this.closeChat();
        this.chatWindow.querySelectorAll('.chat-mode-btn').forEach(btn => {
            btn.onclick = (e) => this.switchMode(e.target.getAttribute('data-mode'));
        });
        const sendBtn = this.chatWindow.querySelector('#chat-send-btn');
        const chatInput = this.chatWindow.querySelector('#chat-input');
        sendBtn.onclick = () => this.sendMessage();

        // 设置输入框事件监听器
        this.setupInputEvents(chatInput);
    }

    // 设置输入框事件监听器
    setupInputEvents(chatInput) {
        const userSuggestions = this.chatWindow.querySelector('#user-suggestions');
        let currentAtPosition = -1;
        let isShowingSuggestions = false;

        chatInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                if (isShowingSuggestions) {
                    // 如果正在显示建议，选择第一个
                    const firstSuggestion = userSuggestions.querySelector('.user-suggestion-item');
                    if (firstSuggestion) {
                        this.selectUser(firstSuggestion, chatInput, currentAtPosition);
                        return;
                    }
                }
                this.sendMessage();
            } else if (e.key === 'ArrowDown' && isShowingSuggestions) {
                e.preventDefault();
                this.navigateSuggestions('down');
            } else if (e.key === 'ArrowUp' && isShowingSuggestions) {
                e.preventDefault();
                this.navigateSuggestions('up');
            } else if (e.key === 'Escape' && isShowingSuggestions) {
                this.hideSuggestions();
            }
        });

        chatInput.addEventListener('input', (e) => {
            const value = e.target.value;
            const cursorPosition = e.target.selectionStart;

            // 查找最近的@符号位置
            const atPosition = this.findAtPosition(value, cursorPosition);

            if (atPosition !== -1) {
                const searchTerm = value.substring(atPosition + 1, cursorPosition);
                currentAtPosition = atPosition;

                if (searchTerm.length >= 0) {
                    this.searchUsers(searchTerm, userSuggestions);
                    isShowingSuggestions = true;
                } else {
                    this.hideSuggestions();
                    isShowingSuggestions = false;
                }
            } else {
                this.hideSuggestions();
                isShowingSuggestions = false;
                currentAtPosition = -1;
            }
        });

        // 点击其他地方隐藏建议
        document.addEventListener('click', (e) => {
            if (!chatInput.contains(e.target) && !userSuggestions.contains(e.target)) {
                this.hideSuggestions();
                isShowingSuggestions = false;
            }
        });
    }

    // 查找@符号位置
    findAtPosition(text, cursorPosition) {
        for (let i = cursorPosition - 1; i >= 0; i--) {
            if (text[i] === '@') {
                // 检查@前面是否是空格或开头
                if (i === 0 || text[i - 1] === ' ' || text[i - 1] === '\n') {
                    return i;
                }
            } else if (text[i] === ' ' || text[i] === '\n') {
                break;
            }
        }
        return -1;
    }

    // 搜索用户
    async searchUsers(searchTerm, suggestionsContainer) {
        try {
            const response = await fetch(`/api/users/search?q=${encodeURIComponent(searchTerm)}`);
            const users = await response.json();

            this.showSuggestions(users, suggestionsContainer);
        } catch (error) {
            console.error('Search User Error:', error);
            this.hideSuggestions();
        }
    }

    // 显示用户建议
    showSuggestions(users, container) {
        if (!users || users.length === 0) {
            this.hideSuggestions();
            return;
        }

        container.innerHTML = users.map((user, index) => `
            <div class="user-suggestion-item ${index === 0 ? 'selected' : ''}"
                 data-user-id="${user.id}"
                 data-user-name="${user.name}"
                 data-user-code="${user.code || ''}">
                <div class="user-avatar">${user.name.charAt(0).toUpperCase()}</div>
                <div class="user-info">
                    <div class="user-name">${user.name}</div>
                    ${user.code ? `<div class="user-code">@${user.code}</div>` : ''}
                </div>
            </div>
        `).join('');

        // 添加点击事件
        container.querySelectorAll('.user-suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                const chatInput = this.chatWindow.querySelector('#chat-input');
                const atPosition = this.findAtPosition(chatInput.value, chatInput.selectionStart);
                this.selectUser(item, chatInput, atPosition);
            });
        });

        container.style.display = 'block';
    }

    // 隐藏建议
    hideSuggestions() {
        const container = this.chatWindow.querySelector('#user-suggestions');
        if (container) {
            container.style.display = 'none';
            container.innerHTML = '';
        }
    }

    // 选择用户
    selectUser(item, chatInput, atPosition) {
        const userName = item.dataset.userName;
        const userCode = item.dataset.userCode;
        const displayName = userCode || userName;

        const value = chatInput.value;
        const beforeAt = value.substring(0, atPosition);
        const afterCursor = value.substring(chatInput.selectionStart);

        const newValue = beforeAt + `@${displayName} ` + afterCursor;
        chatInput.value = newValue;

        // 设置光标位置
        const newCursorPosition = beforeAt.length + displayName.length + 2;
        chatInput.setSelectionRange(newCursorPosition, newCursorPosition);

        this.hideSuggestions();
        chatInput.focus();
    }

    // 导航建议列表
    navigateSuggestions(direction) {
        const container = this.chatWindow.querySelector('#user-suggestions');
        const items = container.querySelectorAll('.user-suggestion-item');
        const currentSelected = container.querySelector('.user-suggestion-item.selected');

        if (!items.length) return;

        let newIndex = 0;
        if (currentSelected) {
            const currentIndex = Array.from(items).indexOf(currentSelected);
            if (direction === 'down') {
                newIndex = (currentIndex + 1) % items.length;
            } else {
                newIndex = (currentIndex - 1 + items.length) % items.length;
            }
            currentSelected.classList.remove('selected');
        }

        items[newIndex].classList.add('selected');
    }

    openChat() {
        this.isChatOpen = true;
        this.chatWindow.style.display = 'flex';
        this.floatBtn.style.display = 'none';

        // 检查是否有未读@消息需要定位
        if (this.hasMentions && this.firstMentionId) {
            // 如果有@消息，先切换到公众聊天模式
            if (this.mode !== 'public') {
                this.switchMode('public');
            }
            // 延迟定位，确保消息已加载
            setTimeout(() => {
                this.scrollToMessage(this.firstMentionId);
            }, 500);
        }

        this.updateBubble(0, false);
        this.updateChatReadStatus(); // 标记聊天为已读

        // 每次打开聊天窗口时刷新内容
        this.refreshChatContent();
    }

    // 刷新聊天内容
    refreshChatContent() {
        // 清除之前的消息
        this.clearMessages();

        // 重置聊天状态
        this.lastMessageTimestamp = null;

        // 根据当前模式和权限重新加载内容
        if (this.mode === 'robot') {
            if (!this.hasPermission('can_chat_robot')) {
                this.showNoPermissionForMode('机器人聊天');
                this.lockInputArea();
            } else {
                this.unlockInputArea();
                // 重新加载机器人知识库
                this.kbList = [];
                this.loadChatbotKB();
            }
        } else if (this.mode === 'public') {
            if (!this.hasPermission('can_chat_public')) {
                this.showNoPermissionForMode('公众聊天');
                this.lockInputArea();
            } else {
                this.unlockInputArea();
                // 重新加载公众聊天消息
                this.startPublicChat();
            }
        }
    }

    closeChat() {
        this.isChatOpen = false;
        this.chatWindow.style.display = 'none';
        this.floatBtn.style.display = 'flex';
        this.stopPublicChat();
    }

    switchMode(mode) {
        if (this.mode === mode) {
            // 即使是相同模式，也刷新内容
            this.refreshModeContent(mode);
            return;
        }

        this.mode = mode;
        this.chatWindow.querySelectorAll('.chat-mode-btn').forEach(btn => {
            btn.classList.toggle('active', btn.getAttribute('data-mode') === mode);
        });

        // 刷新模式内容
        this.refreshModeContent(mode);
    }

    // 刷新特定模式的内容
    refreshModeContent(mode) {
        this.clearMessages();
        this.lastMessageTimestamp = null;

        // 检查权限并相应地设置界面状态
        if (mode === 'robot') {
            this.stopPublicChat();
            if (!this.hasPermission('can_chat_robot')) {
                this.showNoPermissionForMode('机器人聊天');
                this.lockInputArea();
            } else {
                this.unlockInputArea();
                // 重新加载机器人知识库和欢迎消息
                this.kbList = [];
                this.loadChatbotKB();
            }
        } else if (mode === 'public') {
            if (!this.hasPermission('can_chat_public')) {
                this.showNoPermissionForMode('公众聊天');
                this.lockInputArea();
            } else {
                this.unlockInputArea();
                // 重新加载公众聊天消息
                this.startPublicChat();
            }
        }
    }

    clearMessages() {
        this.chatWindow.querySelector('.chat-messages').innerHTML = '';
        this.lastMessageTimestamp = null;
    }

    // 处理消息中的@用户高亮
    highlightMentions(content) {
        if (!content) return content;

        // 匹配@用户名或@用户代码的正则表达式
        // 匹配@后跟非空白字符，直到遇到空格、换行或字符串结束
        const mentionRegex = /@([^\s]+)/g;

        return content.replace(mentionRegex, '<span class="mention">@$1</span>');
    }

    appendMessage(msg, from) {
        const msgContainer = this.chatWindow.querySelector('.chat-messages');
        // 日期分隔
        let showDate = false;
        let msgTime = null;
        if (msg.created_at) {
            msgTime = new Date(msg.created_at.replace(' ', 'T'));
            if (!this._lastMsgDate || this._lastMsgDate !== msgTime.toDateString()) {
                showDate = true;
                this._lastMsgDate = msgTime.toDateString();
            }
        }
        if (showDate) {
            const dateDiv = document.createElement('div');
            dateDiv.className = 'chat-date-divider';
            dateDiv.textContent = msgTime.toLocaleDateString();
            msgContainer.appendChild(dateDiv);
        }
        const msgDiv = document.createElement('div');
        // 统一样式：自己发的都是 chat-msg-user，机器人和他人都是 chat-msg-other
        if (from === 'user' || from === 'user-self') {
            msgDiv.className = 'chat-msg chat-msg-user';
        } else if (from === 'other' || from === 'bot') {
            msgDiv.className = 'chat-msg chat-msg-other';
        } else {
            msgDiv.className = `chat-msg chat-msg-${from}`;
        }

        // 为公众聊天消息添加唯一ID
        if (msg.id && (from === 'user' || from === 'other')) {
            msgDiv.id = `chat-msg-${msg.id}`;
        }
        // 处理姓名和时间
        let name = '';
        let timeStr = '';
        if (from === 'user' || from === 'other') {
            // 公众模式
            if (msg.user_id === window.currentUserId) {
                name = 'Me';
            } else {
                // 显示格式：名字(部门)
                const userName = msg.user_name || 'Unknown User';
                const departmentName = msg.department_name;
                if (departmentName) {
                    name = `${userName}(${departmentName})`;
                } else {
                    name = userName;
                }
            }
            if (msg.created_at) {
                const t = new Date(msg.created_at.replace(' ', 'T'));
                timeStr = t.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            }
            const highlightedContent = this.highlightMentions(msg.content);
            msgDiv.innerHTML = `<div class=\"chat-msg-meta\"><span class=\"chat-msg-username\">${name}</span>: <span class=\"chat-msg-time\">${timeStr}</span></div><div class=\"chat-msg-bubble\">${highlightedContent}</div>`;
        } else if (from === 'user-self') {
            // 机器人模式自己发的
            name = '我';
            const now = new Date();
            timeStr = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            const highlightedContent = this.highlightMentions(msg);
            msgDiv.innerHTML = `<div class=\"chat-msg-meta\"><span class=\"chat-msg-username\">${name}</span>: <span class=\"chat-msg-time\">${timeStr}</span></div><div class=\"chat-msg-bubble\">${highlightedContent}</div>`;
        } else if (from === 'bot') {
            // 机器人回复
            name = 'Robot';
            const now = new Date();
            timeStr = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            msgDiv.innerHTML = `<div class=\"chat-msg-meta\"><span class=\"chat-msg-username\">${name}</span>: <span class=\"chat-msg-time\">${timeStr}</span></div><div class=\"chat-msg-bubble\">${msg}</div>`;
        } else {
            msgDiv.textContent = msg;
        }
        msgContainer.appendChild(msgDiv);
        msgContainer.scrollTop = msgContainer.scrollHeight;
    }

    sendMessage() {
        const input = this.chatWindow.querySelector('#chat-input');
        const text = input.value.trim();
        if (!text || input.disabled) return; // 如果输入框被禁用，直接返回

        input.value = '';

        if (this.mode === 'robot') {
            this.appendMessage(text, 'user-self'); // Special class for user's own message in robot mode
            this.replyRobot(text);
        } else {
            this.sendPublicMessage(text);
        }
    }

    // Robot Mode
    loadChatbotKB() {
        fetch('/api/chatbot-kb').then(res => res.json()).then(list => {
            this.kbList = list;
            this.appendMessage('Hello，I Am Robot，How Can I Help？', 'bot');
        }).catch(() => this.appendMessage('Robot Initialization Failed', 'bot'));
    }

    replyRobot(text) {
        // 改为包含匹配：只要用户输入的文本包含关键词就匹配
        const found = this.kbList.find(item => {
            // 不区分大小写的包含匹配
            return text.toLowerCase().includes(item.keyword.toLowerCase());
        });

        setTimeout(() => {
            const defaultReply = window.t ? window.t('robot_no_answer') : '抱歉，我无法回答这个问题。';
            this.appendMessage(found ? found.answer : defaultReply, 'bot');
        }, 300);
    }

    // Public Chat Mode
    startPublicChat() {
        this.loadPublicMessages();
        if (this.publicChatPollInterval) clearInterval(this.publicChatPollInterval);
        this.publicChatPollInterval = setInterval(() => this.loadPublicMessages(true), 5000);
    }

    stopPublicChat() {
        if (this.publicChatPollInterval) {
            clearInterval(this.publicChatPollInterval);
            this.publicChatPollInterval = null;
        }
    }

    loadPublicMessages(isPolling = false) {
        let url = '/api/public-chat-messages';
        if (isPolling && this.lastMessageTimestamp) {
            url += `?after=${this.lastMessageTimestamp}`;
        }

        fetch(url).then(res => res.json()).then(messages => {
            if (!messages || messages.length === 0) return;

            messages.forEach(msg => {
                const from = (msg.user_id === window.currentUserId) ? 'user' : 'other';
                this.appendMessage(msg, from);
            });
            
            this.lastMessageTimestamp = messages[messages.length - 1].created_at;

            if (!this.isChatOpen && isPolling) {
                // 有新消息时，重新检查@消息状态
                this.checkMentionStatus();
            }
        }).catch(console.error);
    }

    sendPublicMessage(text) {
        fetch('/api/public-chat-messages', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ content: text, user_name: window.currentUserName })
        }).then(res => res.json()).then(data => {
            if (data.success) {
                this.loadPublicMessages(true);
            } else {
                this.appendMessage(`发送失败: ${data.error}`, 'bot');
            }
        }).catch(() => this.appendMessage('发送失败，请检查网络', 'bot'));
    }

    // 开始检查@消息
    startMentionCheck() {
        // 立即检查一次
        this.checkMentionStatus();

        // 每30秒检查一次@消息状态
        this.mentionCheckInterval = setInterval(() => {
            if (!this.isChatOpen) { // 只在聊天窗口关闭时检查
                this.checkMentionStatus();
            }
        }, 30000);
    }

    // 检查@消息状态
    checkMentionStatus() {
        fetch('/api/chat-mention-status')
            .then(res => res.json())
            .then(data => {
                this.firstMentionId = data.first_mention_id;
                if (data.has_mentions) {
                    this.updateBubble(data.total_unread, true);
                } else if (data.total_unread > 0) {
                    this.updateBubble(data.total_unread, false);
                } else {
                    this.updateBubble(0, false);
                }
            })
            .catch(console.error);
    }

    // 更新聊天阅读状态
    updateChatReadStatus() {
        fetch('/api/chat-read-status', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        }).catch(console.error);
    }

    // 滚动到指定消息
    scrollToMessage(messageId) {
        const messageElement = document.getElementById(`chat-msg-${messageId}`);
        if (messageElement) {
            const chatMessages = this.chatWindow.querySelector('.chat-messages');

            // 滚动到消息位置
            messageElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });

            // 高亮显示@消息
            messageElement.style.backgroundColor = '#fff3cd';
            messageElement.style.border = '2px solid #ffc107';
            messageElement.style.borderRadius = '8px';

            // 3秒后移除高亮
            setTimeout(() => {
                messageElement.style.backgroundColor = '';
                messageElement.style.border = '';
                messageElement.style.borderRadius = '';
            }, 3000);
        }
    }
}

// 初始化
window.addEventListener('DOMContentLoaded', () => {
    window.chatModule = new ChatModule();
}); 