"""
后端国际化模块
提供服务器端消息的多语言支持
"""

from flask import session

# 后端消息翻译字典
BACKEND_TRANSLATIONS = {
    'en': {
        # 认证相关
        'not_authenticated': 'Not authenticated',
        'access_denied': 'Access denied',
        'invalid_card_id': 'Invalid employee card ID',
        'login_required': 'Please login first',
        'invalid_password': 'Invalid password',
        'too_many_failed_attempts': 'Too many failed attempts. Please reset your password or contact administrator.',
        'password_changed_success': 'Password changed successfully',
        'password_removed_success': 'Password removed successfully',
        
        # 用户管理
        'user_created_success': 'User created successfully',
        'user_updated_success': 'User updated successfully',
        'user_deleted_success': 'User deleted successfully',
        'card_id_exists': 'Employee card ID already exists',
        'data_integrity_error': 'Data integrity error',
        
        # 部门管理
        'department_created_success': 'Department created successfully',
        'department_updated_success': 'Department updated successfully',
        'department_deleted_success': 'Department deleted successfully',
        'department_name_exists': 'Department name already exists',
        
        # 公告管理
        'announcement_created_success': 'Announcement published successfully',
        'announcement_updated_success': 'Announcement updated successfully',
        'announcement_deleted_success': 'Announcement deleted successfully',
        
        # 聊天相关
        'no_chat_permission': 'You do not have chat permission',
        'no_public_chat_permission': 'You do not have public chat permission',
        'no_robot_chat_permission': 'You do not have robot chat permission',
        'message_empty': 'Message content cannot be empty',
        
        # 机器人知识库
        'chatbot_kb_created_success': 'Knowledge base entry created successfully',
        'chatbot_kb_updated_success': 'Knowledge base entry updated successfully',
        'chatbot_kb_deleted_success': 'Knowledge base entry deleted successfully',
        'keyword_exists': 'Keyword already exists',
        
        # 通用错误
        'no_data_provided': 'No data provided',
        'operation_failed': 'Operation failed',
        'operation_success': 'Operation successful',
        'network_error': 'Network error, please try again',
        'server_error': 'Server error, please contact administrator',
        'invalid_language': 'Invalid language',
        
        # 权限相关
        'permissions_updated_success': 'Permissions updated successfully',
        'no_backend_permission': 'You do not have backend management permission',
        
        # 问题解决
        'problem_solve_created_success': 'Problem report submitted successfully',
        'problem_solve_replied_success': 'Problem reply sent successfully',
        'problem_solve_deleted_success': 'Problem report deleted successfully',
        
        # 标签相关
        'label_printed_success': 'Label printed successfully',
        'printer_not_found': 'Printer not found',
        'print_failed': 'Print failed',
        
        # 审计日志
        'audit_log_cleared': 'Audit log cleared successfully',
        
        # 系统设置
        'settings_updated_success': 'System settings updated successfully',
        
        # 条码前缀
        'barcode_prefix_created_success': 'Barcode prefix created successfully',
        'barcode_prefix_updated_success': 'Barcode prefix updated successfully',
        'barcode_prefix_deleted_success': 'Barcode prefix deleted successfully',
        'prefix_exists': 'Prefix already exists',

        # 通知标题
        'feedback_reply_notification_title': 'Your feedback has been replied',
        'problem_reply_notification_title': 'Your problem has been replied',
        'feedback_reply_notification_content': 'Your feedback about "{title}" has been replied: {reply}',
        'problem_reply_notification_content': 'Your problem about label "{label}" has been replied: {reply}'
    },
    
    'es': {
        # 认证相关
        'not_authenticated': 'No autenticado',
        'access_denied': 'Acceso denegado',
        'invalid_card_id': 'ID de tarjeta de empleado inválido',
        'login_required': 'Por favor, inicie sesión primero',
        'invalid_password': 'Contraseña inválida',
        'too_many_failed_attempts': 'Demasiados intentos fallidos. Por favor restablezca su contraseña o contacte al administrador.',
        'password_changed_success': 'Contraseña cambiada exitosamente',
        'password_removed_success': 'Contraseña eliminada exitosamente',
        
        # 用户管理
        'user_created_success': 'Usuario creado exitosamente',
        'user_updated_success': 'Usuario actualizado exitosamente',
        'user_deleted_success': 'Usuario eliminado exitosamente',
        'card_id_exists': 'El ID de tarjeta de empleado ya existe',
        'data_integrity_error': 'Error de integridad de datos',
        
        # 部门管理
        'department_created_success': 'Departamento creado exitosamente',
        'department_updated_success': 'Departamento actualizado exitosamente',
        'department_deleted_success': 'Departamento eliminado exitosamente',
        'department_name_exists': 'El nombre del departamento ya existe',
        
        # 公告管理
        'announcement_created_success': 'Anuncio publicado exitosamente',
        'announcement_updated_success': 'Anuncio actualizado exitosamente',
        'announcement_deleted_success': 'Anuncio eliminado exitosamente',
        
        # 聊天相关
        'no_chat_permission': 'No tiene permisos de chat',
        'no_public_chat_permission': 'No tiene permisos de chat público',
        'no_robot_chat_permission': 'No tiene permisos de chat con robot',
        'message_empty': 'El contenido del mensaje no puede estar vacío',
        
        # 机器人知识库
        'chatbot_kb_created_success': 'Entrada de base de conocimientos creada exitosamente',
        'chatbot_kb_updated_success': 'Entrada de base de conocimientos actualizada exitosamente',
        'chatbot_kb_deleted_success': 'Entrada de base de conocimientos eliminada exitosamente',
        'keyword_exists': 'La palabra clave ya existe',
        
        # 通用错误
        'no_data_provided': 'No se proporcionaron datos',
        'operation_failed': 'Operación fallida',
        'operation_success': 'Operación exitosa',
        'network_error': 'Error de red, por favor intente de nuevo',
        'server_error': 'Error del servidor, por favor contacte al administrador',
        'invalid_language': 'Idioma inválido',
        
        # 权限相关
        'permissions_updated_success': 'Permisos actualizados exitosamente',
        'no_backend_permission': 'No tiene permisos de gestión del backend',
        
        # 问题解决
        'problem_solve_created_success': 'Reporte de problema enviado exitosamente',
        'problem_solve_replied_success': 'Respuesta al problema enviada exitosamente',
        'problem_solve_deleted_success': 'Reporte de problema eliminado exitosamente',
        
        # 标签相关
        'label_printed_success': 'Etiqueta impresa exitosamente',
        'printer_not_found': 'Impresora no encontrada',
        'print_failed': 'Impresión fallida',
        
        # 审计日志
        'audit_log_cleared': 'Registro de auditoría limpiado exitosamente',
        
        # 系统设置
        'settings_updated_success': 'Configuración del sistema actualizada exitosamente',
        
        # 条码前缀
        'barcode_prefix_created_success': 'Prefijo de código de barras creado exitosamente',
        'barcode_prefix_updated_success': 'Prefijo de código de barras actualizado exitosamente',
        'barcode_prefix_deleted_success': 'Prefijo de código de barras eliminado exitosamente',
        'prefix_exists': 'El prefijo ya existe',

        # 通知标题
        'feedback_reply_notification_title': 'Su comentario ha sido respondido',
        'problem_reply_notification_title': 'Su problema ha sido respondido',
        'feedback_reply_notification_content': 'Su comentario sobre "{title}" ha sido respondido: {reply}',
        'problem_reply_notification_content': 'Su problema sobre la etiqueta "{label}" ha sido respondido: {reply}'
    },
    
    'zh': {
        # 认证相关
        'not_authenticated': '未认证',
        'access_denied': '访问被拒绝',
        'invalid_card_id': '无效的员工卡ID',
        'login_required': '请先登录',
        'invalid_password': '密码错误',
        'too_many_failed_attempts': '失败次数过多。请重置密码或联系管理员。',
        'password_changed_success': '密码修改成功',
        'password_removed_success': '密码已移除',
        
        # 用户管理
        'user_created_success': '用户创建成功',
        'user_updated_success': '用户更新成功',
        'user_deleted_success': '用户删除成功',
        'card_id_exists': '员工卡ID已存在',
        'data_integrity_error': '数据完整性错误',
        
        # 部门管理
        'department_created_success': '部门创建成功',
        'department_updated_success': '部门更新成功',
        'department_deleted_success': '部门删除成功',
        'department_name_exists': '部门名称已存在',
        
        # 公告管理
        'announcement_created_success': '公告发布成功',
        'announcement_updated_success': '公告更新成功',
        'announcement_deleted_success': '公告删除成功',
        
        # 聊天相关
        'no_chat_permission': '您没有聊天权限',
        'no_public_chat_permission': '您没有公众聊天权限',
        'no_robot_chat_permission': '您没有机器人聊天权限',
        'message_empty': '消息内容不能为空',
        
        # 机器人知识库
        'chatbot_kb_created_success': '词条创建成功',
        'chatbot_kb_updated_success': '词条更新成功',
        'chatbot_kb_deleted_success': '词条删除成功',
        'keyword_exists': '关键词已存在',
        
        # 通用错误
        'no_data_provided': '未提供数据',
        'operation_failed': '操作失败',
        'operation_success': '操作成功',
        'network_error': '网络错误，请重试',
        'server_error': '服务器错误，请联系管理员',
        'invalid_language': '无效的语言',
        
        # 权限相关
        'permissions_updated_success': '权限更新成功',
        'no_backend_permission': '您没有后台管理权限',
        
        # 问题解决
        'problem_solve_created_success': '问题报告提交成功',
        'problem_solve_replied_success': '问题回复发送成功',
        'problem_solve_deleted_success': '问题报告删除成功',
        
        # 标签相关
        'label_printed_success': '标签打印成功',
        'printer_not_found': '未找到打印机',
        'print_failed': '打印失败',
        
        # 审计日志
        'audit_log_cleared': '审计日志清理成功',
        
        # 系统设置
        'settings_updated_success': '系统设置更新成功',
        
        # 条码前缀
        'barcode_prefix_created_success': '条码前缀创建成功',
        'barcode_prefix_updated_success': '条码前缀更新成功',
        'barcode_prefix_deleted_success': '条码前缀删除成功',
        'prefix_exists': '前缀已存在',

        # 通知标题
        'feedback_reply_notification_title': '您的反馈已得到回复',
        'problem_reply_notification_title': '您的问题已得到回复',
        'feedback_reply_notification_content': '关于"{title}"的反馈已得到回复：{reply}',
        'problem_reply_notification_content': '关于标签"{label}"的问题已得到回复：{reply}'
    }
}

def get_user_language():
    """获取当前用户的语言设置"""
    return session.get('language', 'en')

def t(key, language=None):
    """
    翻译函数
    :param key: 翻译键
    :param language: 指定语言，如果不指定则使用当前用户语言
    :return: 翻译后的文本
    """
    if language is None:
        language = get_user_language()
    
    # 如果语言不存在，默认使用英文
    if language not in BACKEND_TRANSLATIONS:
        language = 'en'
    
    # 返回翻译，如果键不存在则返回键本身
    return BACKEND_TRANSLATIONS[language].get(key, key)

def get_error_response(key, status_code=400, language=None):
    """
    获取错误响应的翻译
    :param key: 错误消息键
    :param status_code: HTTP状态码
    :param language: 指定语言
    :return: 翻译后的错误消息
    """
    return {'error': t(key, language)}, status_code

def get_success_response(key, language=None, **kwargs):
    """
    获取成功响应的翻译
    :param key: 成功消息键
    :param language: 指定语言
    :param kwargs: 额外的响应数据
    :return: 翻译后的成功消息
    """
    response = {'success': True, 'message': t(key, language)}
    response.update(kwargs)
    return response
