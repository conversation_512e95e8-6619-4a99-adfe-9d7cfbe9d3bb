/* General Body */
body {
    font-family: 'Segoe UI', 'Microsoft YaHei', <PERSON>l, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0;
    color: #333;
    min-height: 100vh;
}

/* Header */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 0 20px;
    height: 70px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

header .header-left, header .header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

header .title {
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

header select, header a {
    text-decoration: none;
    color: #333;
    padding: 10px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    font-weight: 500;
}

header select {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(102, 126, 234, 0.3);
    cursor: pointer;
}

header a:hover {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-admin, .btn-problem-solve {
    font-weight: 600;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
}

.btn-admin:hover, .btn-problem-solve:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}



.user-info {
    font-weight: 600;
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    padding: 8px 12px;
    border-radius: 6px;
}

.notification-bell {
    position: relative;
    display: inline-block;
    font-size: 1.4rem;
    cursor: pointer;
    color: #667eea;
    vertical-align: middle;
}

.notification-bell .bell-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    left: auto;
    transform: none;
}

.bell-badge {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: #dc3545;
    color: white;
    border-radius: 50%;
    padding: 2px 6px;
    font-size: 0.7rem;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
    font-weight: bold;
    pointer-events: none;
    z-index: 2;
}

.btn-problem-solve {
    position: relative;
    display: inline-block;
}

.btn-problem-solve .bell-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    left: auto;
    transform: none;
}

/* Marquee */
.marquee {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    padding: 0;
    margin: 0 20px;
    overflow: hidden;
    display: flex;
    align-items: center;
    height: 40px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.marquee p {
    color: red;
    white-space: nowrap;
    animation: marquee 10s linear infinite;
    font-weight: 500;
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0 20px;
    width: 100%;
}

.marquee p::before {
    content: "📢";
    font-size: 16px;
    flex-shrink: 0;
}

@keyframes marquee {
    0% {
        transform: translateX(100%);
    }
    100% {
        transform: translateX(-100%);
    }
}

/* Main content */
main {
    padding: 30px;
    max-width: 1400px;
    margin: 0 auto;
}



.label-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 20px;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

.label-grid button {
    padding: 25px 20px;
    font-size: 1.1rem;
    font-weight: 600;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.label-grid button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.label-grid button:hover::before {
    left: 100%;
}

.label-grid button:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.label-grid button:active {
    transform: translateY(-2px) scale(1.01);
}

/* Chat float */
.chat-float {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 2.2rem;
    cursor: pointer;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    z-index: 1000;
}

.chat-float:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
}

/* Admin Page */
.admin-container {
    display: flex;
    min-height: calc(100vh - 70px);
}

.admin-nav {
    width: 250px;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: #fff;
    height: calc(100vh - 70px);
    box-shadow: 4px 0 15px rgba(0,0,0,0.1);
}

.admin-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.admin-nav ul li {
    position: relative;
}

.admin-nav ul li a {
    position: relative;
    display: flex;
    align-items: center;
    padding: 18px 20px;
    color: #ecf0f1;
    text-decoration: none;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    transition: all 0.3s ease;
    font-weight: 500;
}

.admin-nav ul li a:hover {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    transform: translateX(5px);
}

.admin-content {
    flex-grow: 1;
    padding: 30px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.admin-content section {
    display: none;
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.admin-content section:first-child {
    display: block;
}

/* Login container */
.login-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    text-align: center;
    width: 400px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.login-container h2 {
    color: #667eea;
    margin-bottom: 30px;
    font-size: 2rem;
    font-weight: 700;
}

.login-container input[type="text"], .login-container input[type="password"] {
    width: 100%;
    padding: 15px;
    margin: 10px 0;
    border: 2px solid rgba(102, 126, 234, 0.3);
    border-radius: 10px;
    font-size: 1.1rem;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.login-container input[type="text"]:focus, .login-container input[type="password"]:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.login-container button {
    width: 100%;
    padding: 15px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 20px;
}

.login-container button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.error {
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
    padding: 10px;
    border-radius: 8px;
    margin-top: 15px;
    border-left: 4px solid #e74c3c;
}

/* Search Section */
.search-section {
    margin-bottom: 20px;
    padding: 0;
    background: none;
    backdrop-filter: none;
    border-radius: 0;
    box-shadow: none;
}

.search-container {
    position: relative;
    max-width: 500px;
    margin: 0 auto;
}

.search-input {
    width: 100%;
    padding: 15px 25px;
    border: 2px solid #e9ecef;
    border-radius: 30px;
    font-size: 18px;
    background: white;
    transition: all 0.3s ease;
    box-sizing: border-box;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 8px 25px rgba(102, 126, 234, 0.2);
    transform: translateY(-2px);
}

/* Search Results Dropdown */
.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    max-height: 400px;
    overflow-y: auto;
    z-index: 10000;
    display: none;
    margin-top: 10px;
}

.search-results.show {
    display: block;
    animation: slideDown 0.3s ease;
}

.search-result-item {
    padding: 15px 20px;
    border-bottom: 1px solid #f8f9fa;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 12px;
}

.search-result-item:first-child {
    border-radius: 15px 15px 0 0;
}

.search-result-item:last-child {
    border-bottom: none;
    border-radius: 0 0 15px 15px;
}

.search-result-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.search-result-item.selected {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.search-result-icon {
    width: 24px;
    height: 24px;
    background: #667eea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    flex-shrink: 0;
}

.search-result-item.selected .search-result-icon {
    background: rgba(255, 255, 255, 0.2);
}

.search-result-content {
    flex: 1;
}

.search-result-name {
    font-weight: 600;
    margin-bottom: 4px;
    font-size: 16px;
}

.search-result-type {
    font-size: 13px;
    color: #6c757d;
    opacity: 0.8;
}

.search-result-item.selected .search-result-type {
    color: rgba(255, 255, 255, 0.8);
}

.no-results {
    padding: 30px 20px;
    text-align: center;
    color: #6c757d;
    font-style: italic;
    font-size: 16px;
}

/* Remove the old search results section */
.search-results-section {
    display: none;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    header {
        flex-direction: column;
        height: auto;
        padding: 15px;
    }
    
    .marquee {
        margin: 10px 0;
    }
    

    
    .label-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 15px;
        padding: 20px;
    }
    
    .admin-container {
        flex-direction: column;
    }
    
    .admin-nav {
        width: 100%;
        height: auto;
    }
}

/* Admin page specific styles */
.admin-actions {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
}

.btn-add, .btn-refresh, .btn-edit, .btn-delete {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.btn-add {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.btn-add:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-refresh {
    background: linear-gradient(45deg, #17a2b8, #6f42c1);
    color: white;
}

.btn-refresh:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(23, 162, 184, 0.4);
}

.btn-edit {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
    color: white;
    padding: 6px 12px;
    font-size: 0.8rem;
}

.btn-edit:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
}

.btn-delete {
    background: linear-gradient(45deg, #dc3545, #e74c3c);
    color: white;
    padding: 6px 12px;
    font-size: 0.8rem;
}

.btn-delete:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

.admin-table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.admin-table th {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 0.9rem;
}

.admin-table td {
    padding: 12px;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
}

.admin-table tbody tr {
    transition: all 0.3s ease;
}

.admin-table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateX(5px);
}

.admin-table tbody tr:last-child td {
    border-bottom: none;
}

.admin-content h2 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.8rem;
    font-weight: 700;
    border-bottom: 3px solid #667eea;
    padding-bottom: 10px;
}

/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: 15px;
    width: 80%;
    max-width: 600px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #eee;
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #667eea;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.form-group input, .form-group select, .form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #eee;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid #eee;
}

.btn-cancel {
    background: #6c757d;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-save {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-save:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

/* Status indicators */
.status-active {
    background: #28a745;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-inactive {
    background: #6c757d;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Loading spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(102, 126, 234, 0.3);
    border-radius: 50%;
    border-top-color: #667eea;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #495057;
}

.empty-state p {
    color: #6c757d;
    font-size: 0.9rem;
}

.label-preview {
    display: flex;
    width: 100%;
    height: 100%;
    border: 1px solid #ccc;
    background: #fff;
}

/* 用户下拉菜单样式 */
.user-dropdown {
    position: relative;
    display: inline-block;
}

.user-info {
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: white;
    background: linear-gradient(45deg, #667eea, #764ba2);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.user-info:hover {
    background: linear-gradient(45deg, #5a6fd8, #6a4190);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    transform: translateY(-1px);
}

.dropdown-arrow {
    font-size: 12px;
    transition: transform 0.2s ease;
}

.user-dropdown:hover .dropdown-arrow {
    transform: rotate(180deg);
}

.user-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 160px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.user-dropdown:hover .user-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.language-menu-item {
    position: relative;
    padding: 12px 16px;
    color: #333;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background-color 0.2s ease;
}

.language-menu-item:hover {
    background-color: #f8f9fa;
}

.submenu-arrow {
    font-size: 12px;
    transition: transform 0.2s ease;
}

.language-menu-item:hover .submenu-arrow {
    transform: translateX(3px);
}

.language-submenu {
    position: absolute;
    top: 0;
    left: 100%;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 140px;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.language-menu-item:hover .language-submenu {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

.language-option {
    padding: 10px 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-radius: 6px;
    margin: 4px;
}

.language-option:hover {
    background-color: #e3f2fd;
}

.language-option.active {
    background-color: #2196f3;
    color: white;
}

.language-option .flag {
    font-size: 16px;
}

.language-option span:last-child {
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .user-dropdown-menu {
        right: 0;
        left: auto;
    }

    .language-submenu {
        left: auto;
        right: 100%;
        transform: translateX(10px);
    }

    .language-menu-item:hover .language-submenu {
        transform: translateX(0);
    }
}





.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-group input[type="number"],
.form-group input[type="range"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-group input[type="range"] {
    width: calc(100% - 50px);
    display: inline-block;
    margin-right: 10px;
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

.form-group small {
    display: block;
    color: #666;
    font-size: 12px;
    margin-top: 3px;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-right: 10px;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-right: 10px;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-primary {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.btn-primary:hover {
    background-color: #0056b3;
}
