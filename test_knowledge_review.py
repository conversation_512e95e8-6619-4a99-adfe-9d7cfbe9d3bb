#!/usr/bin/env python3
"""
测试知识审核功能
"""
import requests
import json

# 测试配置
BASE_URL = 'http://localhost:5000'
TEST_USER = {
    'username': 'admin',
    'password': 'admin123'
}

def login():
    """登录获取session"""
    session = requests.Session()
    
    # 登录
    login_data = {
        'username': TEST_USER['username'],
        'password': TEST_USER['password']
    }
    
    response = session.post(f'{BASE_URL}/login', data=login_data)
    if response.status_code == 200:
        print("✅ 登录成功")
        return session
    else:
        print(f"❌ 登录失败: {response.status_code}")
        return None

def submit_knowledge(session):
    """提交知识"""
    knowledge_data = {
        'question': '测试关键词',
        'answer': '这是测试回答内容'
    }
    
    response = session.post(
        f'{BASE_URL}/api/submit-knowledge',
        headers={'Content-Type': 'application/json'},
        data=json.dumps(knowledge_data)
    )
    
    if response.status_code == 200:
        print("✅ 知识提交成功")
        return True
    else:
        print(f"❌ 知识提交失败: {response.status_code} - {response.text}")
        return False

def get_pending_submissions(session):
    """获取待审核的知识提交"""
    response = session.get(f'{BASE_URL}/api/knowledge-submissions/pending')
    
    if response.status_code == 200:
        submissions = response.json()
        print(f"✅ 获取到 {len(submissions)} 个待审核提交")
        return submissions
    else:
        print(f"❌ 获取待审核提交失败: {response.status_code}")
        return []

def approve_submission(session, submission_id):
    """审核通过知识提交"""
    review_data = {
        'action': 'approved'
    }
    
    response = session.post(
        f'{BASE_URL}/api/knowledge-submissions/{submission_id}/review',
        headers={'Content-Type': 'application/json'},
        data=json.dumps(review_data)
    )
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✅ 知识审核通过成功")
            return True
        else:
            print(f"❌ 知识审核失败: {result.get('error')}")
            return False
    else:
        print(f"❌ 知识审核请求失败: {response.status_code} - {response.text}")
        return False

def check_chatbot_kb(session):
    """检查聊天机器人知识库"""
    response = session.get(f'{BASE_URL}/api/chatbot-kb')
    
    if response.status_code == 200:
        kb_items = response.json()
        print(f"✅ 聊天机器人知识库有 {len(kb_items)} 条记录")
        
        # 查找我们的测试记录
        test_item = None
        for item in kb_items:
            if item.get('keyword') == '测试关键词':
                test_item = item
                break
        
        if test_item:
            print(f"✅ 找到测试记录: 关键词='{test_item['keyword']}', 回答='{test_item['answer']}'")
            return True
        else:
            print("❌ 未找到测试记录")
            return False
    else:
        print(f"❌ 获取知识库失败: {response.status_code}")
        return False

def main():
    print("=== 知识审核功能测试 ===")
    
    # 登录
    session = login()
    if not session:
        return
    
    # 提交知识
    if not submit_knowledge(session):
        return
    
    # 获取待审核提交
    submissions = get_pending_submissions(session)
    if not submissions:
        print("❌ 没有待审核的提交")
        return
    
    # 找到我们的测试提交
    test_submission = None
    for submission in submissions:
        if submission.get('question') == '测试关键词':
            test_submission = submission
            break
    
    if not test_submission:
        print("❌ 未找到测试提交")
        return
    
    print(f"📝 找到测试提交: ID={test_submission['id']}, 问题='{test_submission['question']}'")
    
    # 审核通过
    if not approve_submission(session, test_submission['id']):
        return
    
    # 检查是否已添加到聊天机器人知识库
    if check_chatbot_kb(session):
        print("🎉 测试完成！知识审核功能正常工作")
    else:
        print("❌ 测试失败！知识未正确添加到聊天机器人知识库")

if __name__ == '__main__':
    main()
