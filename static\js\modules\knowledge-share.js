/**
 * Knowledge Share Module
 * Allows users to submit knowledge for chatbot knowledge base
 */
class KnowledgeShareModule {
    constructor() {
        this.isInitialized = false;
        this.init();
    }

    init() {
        if (this.isInitialized) return;

        this.setupKnowledgeShareButton();
        this.setupReviewButton();
        this.isInitialized = true;
    }

    // Setup the knowledge share button (💡 icon)
    setupKnowledgeShareButton() {
        const chatModeButtons = document.querySelector('.chat-mode-switcher');
        if (!chatModeButtons) {
            // If chat window doesn't exist yet, wait for it
            setTimeout(() => this.setupKnowledgeShareButton(), 1000);
            return;
        }

        // Create knowledge share button
        const knowledgeBtn = document.createElement('button');
        knowledgeBtn.className = 'knowledge-share-btn';
        knowledgeBtn.innerHTML = '💡';
        knowledgeBtn.title = this.t('share_knowledge') || 'Share Knowledge';
        knowledgeBtn.style.cssText = `
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            margin-left: 8px;
            border-radius: 50%;
            transition: background-color 0.2s;
        `;

        knowledgeBtn.addEventListener('mouseenter', () => {
            knowledgeBtn.style.backgroundColor = 'rgba(0,0,0,0.1)';
        });

        knowledgeBtn.addEventListener('mouseleave', () => {
            knowledgeBtn.style.backgroundColor = 'transparent';
        });

        knowledgeBtn.addEventListener('click', () => {
            this.showKnowledgeShareModal();
        });

        chatModeButtons.appendChild(knowledgeBtn);
    }

    // Setup the review button (⚙️ icon) - only visible to users with review permission
    setupReviewButton() {
        const chatModeButtons = document.querySelector('.chat-mode-switcher');
        if (!chatModeButtons) {
            // If chat window doesn't exist yet, wait for it
            setTimeout(() => this.setupReviewButton(), 1000);
            return;
        }

        // Check if user has review permission
        const permissions = window.userPermissions || {};
        if (!permissions.can_review_knowledge_submissions) return;

        // Create review button
        const reviewBtn = document.createElement('button');
        reviewBtn.className = 'knowledge-review-btn';
        reviewBtn.innerHTML = '⚙️';
        reviewBtn.title = this.t('review_knowledge') || 'Review Knowledge Submissions';
        reviewBtn.style.cssText = `
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            padding: 8px;
            margin-left: 8px;
            border-radius: 50%;
            transition: background-color 0.2s;
        `;

        reviewBtn.addEventListener('mouseenter', () => {
            reviewBtn.style.backgroundColor = 'rgba(0,0,0,0.1)';
        });

        reviewBtn.addEventListener('mouseleave', () => {
            reviewBtn.style.backgroundColor = 'transparent';
        });

        reviewBtn.addEventListener('click', () => {
            this.showReviewModal();
        });

        chatModeButtons.appendChild(reviewBtn);
    }

    // Show knowledge share modal
    showKnowledgeShareModal() {
        const modal = document.createElement('div');
        modal.className = 'knowledge-share-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        `;

        const modalContent = document.createElement('div');
        modalContent.className = 'knowledge-share-modal-content';
        modalContent.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 15px;
            width: 500px;
            max-width: 90vw;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        `;

        modalContent.innerHTML = `
            <div class="knowledge-share-header" style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                border-bottom: 2px solid #f0f0f0;
                padding-bottom: 15px;
            ">
                <h3 style="margin: 0; color: #333; font-size: 1.5em;">
                    💡 ${this.t('share_my_knowledge') || 'Share my knowledge.'}
                </h3>
                <button class="close-btn" style="
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #666;
                ">&times;</button>
            </div>
            
            <div class="knowledge-share-example" style="
                background: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                margin-bottom: 20px;
                font-size: 0.9em;
                color: #666;
            ">
                <div style="margin-bottom: 8px;">
                    <strong>${this.t('Example') || 'Example'}:</strong> Question(Keyword)=Answer
                </div>
                <div style="font-style: italic;">
                    What is your name?(your name)=My name is Robot.
                </div>
            </div>

            <form class="knowledge-share-form">
                <div class="form-group" style="margin-bottom: 20px;">
                    <label style="
                        display: block;
                        margin-bottom: 8px;
                        font-weight: 600;
                        color: #333;
                    ">${this.t('question_keyword') || 'Question(Keyword):'} <span style="color: red;">*</span></label>
                    <input type="text" id="knowledgeQuestion" required style="
                        width: 100%;
                        padding: 12px;
                        border: 2px solid #e1e5e9;
                        border-radius: 6px;
                        font-family: inherit;
                        box-sizing: border-box;
                    " placeholder="${this.t('question_placeholder') || 'Enter your question or keyword...'}">
                </div>

                <div class="form-group" style="margin-bottom: 25px;">
                    <label style="
                        display: block;
                        margin-bottom: 8px;
                        font-weight: 600;
                        color: #333;
                    ">${this.t('answer') || 'Answer:'} <span style="color: red;">*</span></label>
                    <textarea id="knowledgeAnswer" required style="
                        width: 100%;
                        padding: 12px;
                        border: 2px solid #e1e5e9;
                        border-radius: 6px;
                        font-family: inherit;
                        resize: vertical;
                        min-height: 100px;
                        box-sizing: border-box;
                    " placeholder="${this.t('answer_placeholder') || 'Enter the answer...'}"></textarea>
                </div>

                <div class="form-actions" style="
                    display: flex;
                    gap: 10px;
                    justify-content: flex-end;
                ">
                    <button type="button" class="cancel-btn" style="
                        padding: 10px 20px;
                        border: 1px solid #ddd;
                        background: #f8f9fa;
                        border-radius: 6px;
                        cursor: pointer;
                    ">${this.t('cancel') || 'Cancel'}</button>
                    <button type="submit" class="submit-btn" style="
                        padding: 10px 20px;
                        background: linear-gradient(45deg, #667eea, #764ba2);
                        color: white;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-weight: 600;
                    ">${this.t('submit') || 'Submit'}</button>
                </div>
            </form>
        `;

        modal.appendChild(modalContent);
        document.body.appendChild(modal);

        // Event listeners
        const closeBtn = modalContent.querySelector('.close-btn');
        const cancelBtn = modalContent.querySelector('.cancel-btn');
        const form = modalContent.querySelector('.knowledge-share-form');

        closeBtn.addEventListener('click', () => modal.remove());
        cancelBtn.addEventListener('click', () => modal.remove());
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) modal.remove();
        });

        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.submitKnowledge(modal);
        });

        // Focus on first input
        modalContent.querySelector('#knowledgeQuestion').focus();
    }

    // Submit knowledge for review
    submitKnowledge(modal) {
        const question = document.getElementById('knowledgeQuestion').value.trim();
        const answer = document.getElementById('knowledgeAnswer').value.trim();

        if (!question || !answer) {
            this.showMessage(this.t('fill_all_fields') || 'Please fill in all required fields.', 'error');
            return;
        }

        const submitBtn = modal.querySelector('.submit-btn');
        submitBtn.disabled = true;
        submitBtn.textContent = this.t('submitting') || 'Submitting...';

        fetch('/api/knowledge-submissions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                question: question,
                answer: answer
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showMessage(
                    this.t('knowledge_submitted_success') || 'Knowledge submitted successfully! It will be reviewed before being added to the knowledge base.',
                    'success'
                );
                modal.remove();
            } else {
                this.showMessage(data.error || (this.t('submission_failed') || 'Submission failed'), 'error');
                submitBtn.disabled = false;
                submitBtn.textContent = this.t('submit') || 'Submit';
            }
        })
        .catch(error => {
            console.error('Knowledge submission error:', error);
            this.showMessage(this.t('submission_failed') || 'Submission failed', 'error');
            submitBtn.disabled = false;
            submitBtn.textContent = this.t('submit') || 'Submit';
        });
    }

    // Show review modal for admins
    showReviewModal() {
        // Fetch pending submissions
        fetch('/api/knowledge-submissions/pending')
            .then(response => response.json())
            .then(submissions => {
                this.displayReviewModal(submissions);
            })
            .catch(error => {
                console.error('Error fetching submissions:', error);
                this.showMessage(this.t('load_failed') || 'Failed to load submissions', 'error');
            });
    }

    // Display review modal with submissions
    displayReviewModal(submissions) {
        const modal = document.createElement('div');
        modal.className = 'knowledge-review-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        `;

        const modalContent = document.createElement('div');
        modalContent.className = 'knowledge-review-modal-content';
        modalContent.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 15px;
            width: 700px;
            max-width: 90vw;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        `;

        modalContent.innerHTML = `
            <div class="knowledge-review-modal-header" style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                border-bottom: 2px solid #f0f0f0;
                padding-bottom: 15px;
            ">
                <h3 style="margin: 0; color: #333;">⚙️ ${this.t('review_knowledge') || 'Review Knowledge Submissions'}</h3>
                <button class="close-btn" style="
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #666;
                ">&times;</button>
            </div>
            <div class="knowledge-review-list">
                ${this.renderSubmissionsList(submissions)}
            </div>
        `;

        modal.appendChild(modalContent);
        document.body.appendChild(modal);

        // Event listeners
        const closeBtn = modalContent.querySelector('.close-btn');
        closeBtn.addEventListener('click', () => modal.remove());

        modal.addEventListener('click', (e) => {
            if (e.target === modal) modal.remove();
        });
    }

    // Render submissions list
    renderSubmissionsList(submissions) {
        if (submissions.length === 0) {
            return `
                <div style="
                    text-align: center;
                    padding: 40px;
                    color: #666;
                    font-style: italic;
                ">
                    ${this.t('no_submissions') || 'No pending submissions'}
                </div>
            `;
        }

        return submissions.map(submission => `
            <div class="submission-item" style="
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 15px;
                background: #fafafa;
            ">
                <div style="margin-bottom: 10px;">
                    <strong>${this.t('submitted_by') || 'Submitted by'}:</strong> ${submission.user_name}
                    <span style="color: #666; font-size: 0.9em; margin-left: 10px;">
                        ${new Date(submission.created_at).toLocaleString()}
                    </span>
                </div>
                <div style="margin-bottom: 15px;">
                    <div style="margin-bottom: 10px;">
                        <strong>${this.t('question') || 'Question'}:</strong>
                        <div style="background: white; padding: 10px; border-radius: 4px; margin-top: 5px;">
                            ${submission.question}
                        </div>
                    </div>
                    <div>
                        <strong>${this.t('answer') || 'Answer'}:</strong>
                        <div style="background: white; padding: 10px; border-radius: 4px; margin-top: 5px;">
                            ${submission.answer}
                        </div>
                    </div>
                </div>
                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button onclick="window.knowledgeShare.reviewSubmission(${submission.id}, 'approved')" style="
                        padding: 8px 16px;
                        background: #28a745;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        font-weight: 600;
                    ">${this.t('approve') || 'Approve'}</button>
                    <button onclick="window.knowledgeShare.reviewSubmission(${submission.id}, 'rejected')" style="
                        padding: 8px 16px;
                        background: #dc3545;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        font-weight: 600;
                    ">${this.t('reject') || 'Reject'}</button>
                </div>
            </div>
        `).join('');
    }

    // Review submission (approve/reject)
    reviewSubmission(submissionId, status) {
        fetch(`/api/knowledge-submissions/${submissionId}/review`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: status
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showMessage(this.t('submission_reviewed') || 'Submission reviewed successfully', 'success');
                // Close modal and refresh
                document.querySelector('.knowledge-review-modal').remove();
            } else {
                this.showMessage(data.error || (this.t('review_failed') || 'Review failed'), 'error');
            }
        })
        .catch(error => {
            console.error('Review error:', error);
            this.showMessage(this.t('review_failed') || 'Review failed', 'error');
        });
    }

    // Translation helper
    t(key) {
        return window.i18n ? window.i18n.t(key) : null;
    }

    // Show message
    showMessage(message, type = 'info') {
        if (window.showMessage) {
            window.showMessage(message, type);
        } else {
            alert(message);
        }
    }

    // Destroy module
    destroy() {
        const knowledgeBtn = document.querySelector('.knowledge-share-btn');
        const reviewBtn = document.querySelector('.knowledge-review-btn');
        if (knowledgeBtn) knowledgeBtn.remove();
        if (reviewBtn) reviewBtn.remove();
        this.isInitialized = false;
    }
}

// Initialize and expose globally
window.knowledgeShare = new KnowledgeShareModule();
window.KnowledgeShareModule = KnowledgeShareModule;
